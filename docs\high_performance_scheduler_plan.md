# 毫秒级精确调度系统设计文档

## 📋 项目概述

### 项目名称
MySQL数据库驱动的毫秒级精确座位预约调度系统

### 项目目标
设计并实现一个高性能的任务调度系统，能够在指定的预约开放时间（reservation_open_time）以毫秒级精度自动执行座位预约任务，误差仅限于网络传输延迟。

### 核心需求
- **时间精度**：±1毫秒（仅受系统时钟限制）
- **网络补偿**：自动补偿网络传输延迟
- **学校适配**：支持不同学校的服务器响应等待时间（0.1-1.0秒）
- **预热功能**：提前准备所有必要资源
- **高可靠性**：>95%的预约成功率
- **低资源消耗**：<100MB内存占用

## 🏗️ 系统架构设计

### 整体架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                    毫秒级精确调度系统                              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                │
│  │ 数据预加载层 │  │ 串行预热层   │  │ 并发执行层   │                │
│  │ 5分钟间隔   │  │ 提前30秒    │  │ 毫秒级精度   │                │
│  │ +优先队列   │  │ +串行稳定   │  │ +高并发     │                │
│  └─────────────┘  └─────────────┘  └─────────────┘                │
└─────────────────────────────────────────────────────────────────┘
```

### 核心组件

#### 1. 数据预加载层 (Data Preloader Layer)
**职责**：提前加载和缓存即将执行的预约任务
- **扫描频率**：每10分钟
- **预加载窗口**：未来10分钟内的任务
- **数据结构**：内存堆（按执行时间排序）
- **优化策略**：避免重复数据库查询

#### 2. 串行预热层 (Serial Preheating Layer)
**职责**：串行预热所有任务，确保稳定性
- **预热时机**：提前30秒开始
- **预热策略**：串行处理，避免服务器压力
- **预热内容**：
  - 用户认证和token获取
  - 请求参数预准备
  - 网络延迟测试
  - 预热间隔控制（100ms）

#### 3. 并发执行层 (Concurrent Execution Layer)
**职责**：在精确时间点并发执行所有预约
- **精度目标**：±1毫秒
- **执行策略**：高并发同时发送请求
- **并发控制**：信号量限制最大并发数
- **时间同步**：所有任务精确同时执行



## 🔧 技术实现方案

### 核心类设计

#### 1. HighPerformanceScheduler (主调度器)
```python
class HighPerformanceScheduler:
    """高性能毫秒级调度器主类"""
    
    # 核心配置
    preload_window: int = 600      # 预加载窗口：10分钟
    preheat_advance: int = 30      # 预热提前时间：30秒
    precision_tolerance: int = 1   # 精度容差：1毫秒
    
    # 主要方法
    def start()                    # 启动调度器
    def stop()                     # 停止调度器
    def get_basic_stats()          # 获取基础统计信息
```

#### 2. TaskPreloader (数据预加载器)
```python
class TaskPreloader:
    """任务数据预加载器"""
    
    def preload_upcoming_tasks()   # 预加载即将执行的任务
    def update_task_cache()        # 更新任务缓存
    def cleanup_expired_tasks()    # 清理过期任务
```

#### 3. SerialPreheatingManager (串行预热管理器)
```python
class SerialPreheatingManager:
    """串行预热管理器"""

    def preheat_tasks_serially()   # 串行预热所有任务
    def preheat_single_task()      # 预热单个任务
    def authenticate_user()        # 用户认证
    def prepare_request_params()   # 准备请求参数
    def test_network_latency()     # 测试网络延迟
```

#### 4. ConcurrentExecutionManager (并发执行管理器)
```python
class ConcurrentExecutionManager:
    """并发执行管理器"""

    def execute_tasks_concurrently()  # 并发执行所有任务
    def execute_single_task()         # 执行单个任务
    def wait_until_precise_time()     # 等待到精确时间
    def send_reservation_request()    # 发送预约请求
```

#### 5. PriorityTaskQueue (优先队列管理器)
```python
class PriorityTaskQueue:
    """优先队列任务管理器"""

    def add_task()                    # 添加任务到队列
    def get_tasks_ready_for_preheating()  # 获取准备预热的任务
    def get_tasks_ready_for_execution()   # 获取准备执行的任务
    def group_tasks_by_execution_time()   # 按执行时间分组任务
```

### 关键算法

#### 1. 串行预热算法
```python
async def preheat_tasks_serially(tasks: List[PrecisionTask]):
    """
    串行预热算法

    流程：
    1. 逐个预热任务，避免服务器压力
    2. 每个任务完成认证、参数准备、延迟测试
    3. 预热间隔100ms，确保稳定性
    4. 所有任务预热完成后等待执行时间
    """

    for task in tasks:
        try:
            # 预热单个任务
            await self._preheat_single_task(task)
            task.is_preheated = True
            task.status = TaskStatus.PREHEATED

            # 预热间隔，避免对服务器造成压力
            await asyncio.sleep(0.1)  # 100ms间隔

        except Exception as e:
            logger.error(f"任务 {task.task_id} 预热失败: {e}")
            task.status = TaskStatus.FAILED
```

#### 2. 并发执行算法
```python
async def execute_tasks_concurrently(tasks: List[PrecisionTask]):
    """
    并发执行算法

    流程：
    1. 等待到精确执行时间点
    2. 同时启动所有任务的执行
    3. 使用信号量控制最大并发数
    4. 立即发送预约请求，最大化成功率
    """

    # 等待到精确执行时间
    target_time = min(task.target_timestamp_ns for task in tasks)
    await self._wait_until_precise_time(target_time)

    # 同时启动所有任务的执行
    execution_tasks = []
    for task in tasks:
        execution_task = asyncio.create_task(
            self._execute_single_task(task)
        )
        execution_tasks.append(execution_task)

    # 等待所有任务完成
    results = await asyncio.gather(*execution_tasks, return_exceptions=True)
    return results
```

#### 3. 高精度时间等待算法
```python
async def _wait_until_precise_time(target_timestamp_ns: int):
    """
    高精度时间等待算法

    流程：
    1. 计算到目标时间的精确间隔
    2. 使用异步sleep到接近时间（保留1ms）
    3. 最后1毫秒使用忙等待
    4. 在精确时间点触发执行
    """

    current_ns = time.time_ns()
    remaining_ns = target_timestamp_ns - current_ns

    # 粗粒度等待（保留1ms）
    if remaining_ns > 1_000_000:  # >1ms
        sleep_time = (remaining_ns - 1_000_000) / 1_000_000_000
        await asyncio.sleep(sleep_time)

    # 精确忙等待（最后1ms）
    while time.time_ns() < target_timestamp_ns:
        pass  # 忙等待
```

#### 4. 网络延迟测试算法
```python
async def test_network_latency(session: aiohttp.ClientSession, base_url: str):
    """
    网络延迟测试算法

    流程：
    1. 发送多次ping请求测量延迟
    2. 计算平均延迟
    3. 在预热阶段完成，执行时直接使用
    """

    latencies = []
    for _ in range(5):  # 测试5次
        start_time = time.time_ns()
        try:
            await session.get(f"{base_url}/api/ping", timeout=aiohttp.ClientTimeout(total=2))
            end_time = time.time_ns()
            latency_ms = (end_time - start_time) / 1_000_000
            latencies.append(latency_ms)
        except:
            latencies.append(100)  # 默认100ms延迟

    # 返回平均延迟
    return sum(latencies) / len(latencies)
```

#### 5. 主调度循环算法
```python
async def _main_scheduler_loop(self):
    """
    主调度循环算法

    流程：
    1. 检查需要预热的任务（串行处理）
    2. 检查需要执行的任务（并发处理）
    3. 按执行时间分组任务
    4. 为每个时间点创建并发执行任务
    """

    while self.running:
        current_time = time.time()

        # 1. 检查需要预热的任务（串行处理）
        preheat_tasks = self.task_queue.get_tasks_ready_for_preheating()
        if preheat_tasks:
            await self.serial_preheating_manager.preheat_tasks_serially(preheat_tasks)

        # 2. 检查需要执行的任务（并发处理）
        execution_tasks = self.task_queue.get_tasks_ready_for_execution()
        if execution_tasks:
            # 按执行时间分组
            execution_groups = self._group_tasks_by_execution_time(execution_tasks)

            for group_time, tasks in execution_groups.items():
                # 为每个时间点创建并发执行任务
                asyncio.create_task(
                    self.concurrent_execution_manager.execute_tasks_concurrently(tasks)
                )

        await asyncio.sleep(0.001)  # 1毫秒检查间隔
```

## 📊 基础性能指标

### 关键性能指标 (KPI)

| 指标名称 | 目标值 | 测量方法 |
|----------|--------|----------|
| **触发精度** | ±1毫秒 | 系统时钟对比 |
| **网络补偿精度** | ±5毫秒 | 服务器时间戳对比 |
| **预热成功率** | >99% | 预热完成统计 |
| **预约成功率** | >95% | 预约结果统计 |
| **内存使用** | <100MB | 基础资源监控 |

### 基础统计数据结构
```python
@dataclass
class BasicStats:
    """基础统计数据结构"""

    # 精度指标
    trigger_precision_ms: float      # 触发精度（毫秒）
    network_compensation_ms: float   # 网络补偿精度（毫秒）

    # 成功率指标
    preheat_success_rate: float      # 预热成功率
    reservation_success_rate: float  # 预约成功率

    # 基础资源指标
    memory_usage_mb: float           # 内存使用（MB）

    # 执行统计
    total_executions: int            # 总执行次数
    successful_executions: int       # 成功执行次数
```

## 🚀 实施计划

### 开发阶段划分

#### 阶段一：核心调度框架 (2天)
**目标**：建立基础的调度框架和数据结构

**任务清单**：
- [ ] 设计核心数据结构（PrecisionTask, SchedulerConfig等）
- [ ] 实现HighPerformanceScheduler主类
- [ ] 实现TaskPreloader数据预加载器
- [ ] 实现优先队列任务管理（预热串行，执行并发）
- [ ] 编写单元测试

**详细设计说明**：

#### 📋 PrecisionTask (精确任务数据结构)
```python
@dataclass
class PrecisionTask:
    """精确调度任务数据结构"""

    # 基础信息（从数据库获取）
    task_id: str                    # 任务唯一标识
    user_id: str                    # 用户ID
    username: str                   # 用户名
    password: str                   # 用户密码

    # 预约信息（从数据库获取）
    seat_id: str                    # 座位ID
    room_id: str                    # 房间ID
    library_id: str                 # 图书馆ID
    reservation_date: date          # 预约日期
    start_time: time               # 开始时间
    end_time: time                 # 结束时间

    # 执行时间
    reservation_open_time: datetime # 预约开放时间（精确到毫秒）
    target_timestamp_ns: int       # 目标执行时间戳（纳秒级）

    # 网络信息（从数据库获取）
    base_url: str                  # 学校服务器地址
    login_endpoint: str            # 登录接口
    reservation_endpoint: str      # 预约接口

    # 运行时状态
    status: TaskStatus             # 任务状态
    is_preheated: bool = False     # 是否已预热
    preheat_start_time: Optional[datetime] = None

    # 预热结果
    auth_token: Optional[str] = None        # 认证token
    session_cookies: Optional[dict] = None  # 会话cookies
    network_latency_ms: Optional[float] = None  # 网络延迟
    prepared_params: Optional[dict] = None  # 预准备的请求参数

    # 执行结果
    execution_result: Optional['ExecutionResult'] = None
```

#### ⚙️ SchedulerConfig (调度器配置)
```python
@dataclass
class SchedulerConfig:
    """调度器配置参数"""

    # 时间配置
    preload_window_seconds: int = 600      # 预加载窗口：10分钟
    preheat_advance_seconds: int = 30      # 预热提前时间：30秒
    precision_tolerance_ms: int = 1        # 精度容差：1毫秒

    # 并发配置
    max_concurrent_executions: int = 50    # 执行时最大并发数
    preheat_serial_mode: bool = True       # 预热采用串行模式

    # 网络配置
    connection_pool_size: int = 100        # HTTP连接池大小（支持高并发）
    request_timeout_seconds: int = 5       # 请求超时时间
    retry_count: int = 2                   # 重试次数

    # 数据库配置
    db_scan_interval_seconds: int = 300    # 数据库扫描间隔：5分钟
    task_cleanup_hours: int = 24           # 任务清理时间：24小时
```

#### 🎯 并发执行策略
```python
class ConcurrentExecutionManager:
    """并发执行管理器"""

    def __init__(self, max_concurrent: int = 50):
        self.max_concurrent = max_concurrent
        self.execution_semaphore = asyncio.Semaphore(max_concurrent)

    async def execute_tasks_concurrently(self, tasks: List[PrecisionTask]):
        """在精确时间点并发执行所有任务"""

        # 等待到精确执行时间
        target_time = min(task.target_timestamp_ns for task in tasks)
        await self._wait_until_precise_time(target_time)

        # 同时启动所有任务的执行
        execution_tasks = []
        for task in tasks:
            execution_task = asyncio.create_task(
                self._execute_single_task(task)
            )
            execution_tasks.append(execution_task)

        # 等待所有任务完成
        results = await asyncio.gather(*execution_tasks, return_exceptions=True)
        return results

    async def _execute_single_task(self, task: PrecisionTask):
        """执行单个任务（带并发控制）"""
        async with self.execution_semaphore:
            try:
                # 使用预热好的连接和参数立即发送请求
                response = await self._send_reservation_request(task)
                task.execution_result = ExecutionResult.from_response(response)
                task.status = TaskStatus.COMPLETED
                return task.execution_result
            except Exception as e:
                task.execution_result = ExecutionResult.from_error(e)
                task.status = TaskStatus.FAILED
                return task.execution_result
```

#### 🔥 串行预热管理器
```python
class SerialPreheatingManager:
    """串行预热管理器"""

    async def preheat_tasks_serially(self, tasks: List[PrecisionTask]):
        """串行预热所有任务"""

        for task in tasks:
            try:
                await self._preheat_single_task(task)
                task.is_preheated = True
                task.status = TaskStatus.PREHEATED

                # 预热间隔，避免对服务器造成压力
                await asyncio.sleep(0.1)  # 100ms间隔

            except Exception as e:
                logger.error(f"任务 {task.task_id} 预热失败: {e}")
                task.status = TaskStatus.FAILED

    async def _preheat_single_task(self, task: PrecisionTask):
        """预热单个任务"""

        # 1. 建立连接
        session = aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(limit=1),
            timeout=aiohttp.ClientTimeout(total=10)
        )

        # 2. 用户认证
        auth_response = await session.post(
            f"{task.base_url}{task.login_endpoint}",
            data={
                'username': task.username,
                'password': task.password
            }
        )

        if auth_response.status == 200:
            task.auth_token = auth_response.headers.get('Authorization')
            task.session_cookies = dict(auth_response.cookies)

        # 3. 测试网络延迟
        latency_start = time.time_ns()
        ping_response = await session.get(f"{task.base_url}/api/ping")
        latency_end = time.time_ns()
        task.network_latency_ms = (latency_end - latency_start) / 1_000_000

        # 4. 准备请求参数
        task.prepared_params = {
            'seat_id': task.seat_id,
            'room_id': task.room_id,
            'library_id': task.library_id,
            'date': task.reservation_date.isoformat(),
            'start_time': task.start_time.isoformat(),
            'end_time': task.end_time.isoformat(),
            'user_id': task.user_id
        }

        await session.close()
```

#### 📊 优先队列任务管理
```python
class PriorityTaskQueue:
    """优先队列任务管理器"""

    def __init__(self):
        self.preheating_queue = PriorityQueue()  # 预热队列（按预热时间排序）
        self.execution_queue = PriorityQueue()   # 执行队列（按执行时间排序）
        self.task_index = {}

    def add_task(self, task: PrecisionTask):
        """添加任务到队列"""

        # 计算预热开始时间
        preheat_time = task.reservation_open_time - timedelta(seconds=30)
        preheat_priority = int(preheat_time.timestamp() * 1000)

        # 执行时间优先级
        exec_priority = task.target_timestamp_ns

        # 添加到预热队列
        self.preheating_queue.put((preheat_priority, task.task_id, task))

        # 添加到执行队列
        self.execution_queue.put((exec_priority, task.task_id, task))

        self.task_index[task.task_id] = task

    def get_tasks_ready_for_preheating(self) -> List[PrecisionTask]:
        """获取准备预热的任务"""
        current_time_ms = int(time.time() * 1000)
        ready_tasks = []

        while not self.preheating_queue.empty():
            priority, task_id, task = self.preheating_queue.queue[0]

            if priority <= current_time_ms and not task.is_preheated:
                self.preheating_queue.get()
                ready_tasks.append(task)
            else:
                break

        return ready_tasks

    def get_tasks_ready_for_execution(self) -> List[PrecisionTask]:
        """获取准备执行的任务"""
        current_time_ns = time.time_ns()
        ready_tasks = []

        while not self.execution_queue.empty():
            priority, task_id, task = self.execution_queue.queue[0]

            if priority <= current_time_ns and task.is_preheated:
                self.execution_queue.get()
                ready_tasks.append(task)
            else:
                break

        return ready_tasks
```

**交付物**：
- 可运行的基础调度器
- 数据预加载功能
- 基础测试用例

#### 阶段二：串行预热系统 (1天)
**目标**：实现串行预热准备功能

**任务清单**：
- [ ] 实现SerialPreheatingManager串行预热管理器
- [ ] 实现单任务预热流程（认证、参数、延迟测试）
- [ ] 实现预热状态管理和错误处理
- [ ] 实现预热间隔控制（避免服务器压力）
- [ ] 编写预热功能单元测试

**交付物**：
- 串行预热系统
- 预热状态管理
- 网络延迟测试功能

#### 阶段三：并发执行系统 (1天)
**目标**：实现高并发精确执行机制

**任务清单**：
- [ ] 实现ConcurrentExecutionManager并发执行管理器
- [ ] 实现高精度时间等待算法
- [ ] 实现并发控制（信号量限制最大并发数）
- [ ] 实现任务分组和批量执行
- [ ] 实现执行结果收集和状态更新

**交付物**：
- 高并发执行系统
- 精确时间触发机制
- 并发控制和结果管理

#### 阶段四：基础统计 (0.5天)
**目标**：实现基础统计功能

**任务清单**：
- [ ] 实现基础统计数据收集
- [ ] 实现执行结果记录
- [ ] 实现简单的成功率统计
- [ ] 实现基础日志记录

**交付物**：
- 基础统计功能
- 执行结果记录
- 简单统计报告

#### 阶段五：测试调优 (1.5天)
**目标**：全面测试和性能调优

**任务清单**：
- [ ] 精度测试和延迟分析
- [ ] 稳定性测试
- [ ] 基础性能调优
- [ ] 文档编写和部署指南
- [ ] 生产环境部署测试

**交付物**：
- 基础测试报告
- 部署文档
- 用户手册

### 时间安排

| 阶段 | 开始日期 | 结束日期 | 工作日 | 主要里程碑 |
|------|----------|----------|--------|------------|
| 阶段一 | Day 1 | Day 2 | 2天 | 核心框架完成 |
| 阶段二 | Day 3 | Day 3 | 1天 | 预热系统完成 |
| 阶段三 | Day 4 | Day 4 | 1天 | 精确触发完成 |
| 阶段四 | Day 5上午 | Day 5中午 | 0.5天 | 基础统计完成 |
| 阶段五 | Day 5下午 | Day 6 | 1.5天 | 系统测试完成 |

**总计**：5个工作日

## ⚠️ 风险评估与应对

### 高风险项

#### 1. 系统时钟漂移
**风险描述**：系统时钟与标准时间不同步，影响触发精度
**影响程度**：高
**应对措施**：
- 实现NTP时间同步机制
- 定期校准系统时钟
- 监控时钟漂移情况

#### 2. 网络延迟抖动
**风险描述**：网络延迟不稳定，影响补偿算法准确性
**影响程度**：高
**应对措施**：
- 实时测量网络延迟
- 使用统计方法预测延迟
- 实现自适应补偿算法

### 中风险项

#### 3. 内存泄漏
**风险描述**：长时间运行可能导致内存泄漏
**影响程度**：中
**应对措施**：
- 严格的资源管理
- 定期内存检查
- 实现内存使用监控

#### 4. 线程竞争
**风险描述**：多线程环境下的资源竞争
**影响程度**：中
**应对措施**：
- 精确的锁设计
- 无锁数据结构
- 线程安全测试

### 低风险项

#### 5. 配置复杂性
**风险描述**：系统配置过于复杂
**影响程度**：低
**应对措施**：
- 提供合理的默认配置
- 编写详细的配置文档
- 实现配置验证机制

## 📈 预期效果

### 性能提升对比

| 方案 | 触发精度 | 预约成功率 | 资源消耗 | 维护复杂度 |
|------|----------|------------|----------|------------|
| **传统定时任务** | ±30秒 | 70% | 低 | 低 |
| **简单轮询** | ±10秒 | 80% | 中 | 中 |
| **本系统** | ±1毫秒 | >95% | 中 | 中 |

### 执行时间线示例（多任务并发）
```
目标时间：07:00:00.000 (5个任务同时执行)

06:59:30.000 - 开始串行预热
  ├─ 任务1预热: 认证+参数+延迟测试 (100ms)
  ├─ 任务2预热: 认证+参数+延迟测试 (100ms)
  ├─ 任务3预热: 认证+参数+延迟测试 (100ms)
  ├─ 任务4预热: 认证+参数+延迟测试 (100ms)
  ├─ 任务5预热: 认证+参数+延迟测试 (100ms)
  └─ 所有任务预热完成 (总计500ms)

06:59:58.000 - 进入精确等待模式
06:59:59.985 - 同时发送5个请求 (并发执行)
07:00:00.000 - 所有请求到达服务器 (精确命中目标时间)
07:00:00.050 - 收到所有服务器响应
```

### 并发策略优势对比

| 执行策略 | 预热稳定性 | 执行速度 | 成功率 | 服务器压力 |
|----------|------------|----------|--------|------------|
| **全串行** | ✅ 高 | ❌ 慢 | ❌ 低 | ✅ 低 |
| **全并发** | ❌ 低 | ✅ 快 | ⚠️ 中等 | ❌ 高 |
| **混合策略** | ✅ 高 | ✅ 快 | ✅ 高 | ✅ 适中 |

### 业务价值
- **提高预约成功率**：从70%提升到95%+
- **减少人工干预**：全自动化精确执行
- **提升用户体验**：稳定可靠的预约服务
- **降低运维成本**：智能监控和自适应优化

## 📚 技术文档

### 部署要求
- **操作系统**：Linux (推荐Ubuntu 20.04+)
- **Python版本**：3.8+
- **内存要求**：最少512MB，推荐1GB
- **网络要求**：稳定的互联网连接，延迟<100ms

### 配置文件示例
```yaml
# scheduler_config.yaml
scheduler:
  preload_window_seconds: 600          # 预加载窗口（秒）
  preheat_advance_seconds: 30          # 预热提前时间（秒）
  precision_tolerance_ms: 1            # 精度容差（毫秒）
  max_concurrent_executions: 50        # 执行时最大并发数
  preheat_serial_mode: true            # 预热采用串行模式

network:
  connection_pool_size: 100            # 连接池大小（支持高并发）
  request_timeout_seconds: 5           # 请求超时（秒）
  retry_count: 2                       # 重试次数

database:
  db_scan_interval_seconds: 300        # 数据库扫描间隔（秒）
  task_cleanup_hours: 24               # 任务清理时间（小时）

logging:
  enable_basic_stats: true             # 启用基础统计
  log_level: INFO                      # 日志级别
  stats_file: "scheduler_stats.log"    # 统计日志文件
```

---

**文档版本**：v1.0  
**创建日期**：2025-07-20  
**最后更新**：2025-07-20  
**作者**：Augment Agent  
**审核状态**：待审核

