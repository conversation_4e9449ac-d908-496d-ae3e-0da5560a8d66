# 学习通座位预约系统

一个自动化的学习通座位预约系统，支持多用户、多服务器部署和智能预约功能。

## 🚀 功能特性

- ✅ **多用户支持**：支持多个用户同时预约
- ✅ **智能预约**：自动处理时间段拆分和冲突检测
- ✅ **多服务器部署**：支持分布式部署，通过 worker_id 区分
- ✅ **数据库支持**：MySQL 数据库版本，支持大规模部署
- ✅ **学校筛选**：支持按学校筛选预约任务
- ✅ **完整测试**：包含完整的测试套件
- ✅ **安全配置**：敏感信息已排除，提供示例配置

## 📁 项目结构

```
├── main.py                    # JSON 配置版本主程序
├── main_db.py                 # 数据库版本主程序
├── remain.py                  # 剩余座位查询
├── config.example.json        # 配置文件示例
├── database_config.example.json # 数据库配置示例
├── requirements.txt           # Python 依赖
├── database/                  # 数据库相关
│   ├── schema.sql            # 数据库表结构
│   └── *.py                  # 数据库操作类
├── utils/                     # 工具类
├── docs/                      # 文档
└── tests/                     # 测试文件
```

## 🛠️ 安装使用

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置文件

复制示例配置文件并填入真实信息：

```bash
cp config.example.json config.json
cp database_config.example.json database_config.json
```

### 3. 运行程序

**JSON 配置版本：**
```bash
python main.py
```

**数据库版本：**
```bash
python main_db.py
```

## 📖 详细文档

- [数据库版本使用说明](README_DATABASE.md)
- [学校筛选功能](SCHOOL_FILTER_SUMMARY.md)
- [技术文档](docs/)

## ⚠️ 注意事项

1. 请勿将包含真实用户名密码的配置文件提交到版本控制
2. 使用前请仔细阅读相关文档
3. 遵守学校相关规定，合理使用预约系统

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

本项目仅供学习交流使用。
