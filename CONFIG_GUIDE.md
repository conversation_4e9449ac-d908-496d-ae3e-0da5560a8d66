# 配置指南

## 配置优先级

系统按以下优先级加载配置：

1. **默认配置** (最低优先级)
2. **配置文件** (`database_config.json`)
3. **环境变量** (最高优先级)

## 配置文件

### 主配置文件：`database_config.json`

```json
{
    "database": {
        "host": "**************",
        "port": 3306,
        "user": "seatmaster",
        "password": "your_password",
        "database": "seat_reservation",
        "charset": "utf8mb4"
    },
    "server": {
        "worker_id": "worker-8083"
    },
    "heartbeat": {
        "main_server_url": "http://**************:8081",
        "worker_host": "localhost",
        "worker_port": 8082,
        "worker_name": "SeatMaster Worker Server",
        "max_concurrent_tasks": 10,
        "supported_operations": ["XUEXITONG_RESERVATION"],
        "interval": 30,
        "timeout": 5,
        "retry_count": 1,
        "status": "ONLINE",
        "current_load": 0
    }
}
```

## 环境变量

### 数据库配置
- `DB_HOST` - 数据库主机地址
- `DB_PORT` - 数据库端口
- `DB_USER` - 数据库用户名
- `DB_PASSWORD` - 数据库密码
- `DB_DATABASE` - 数据库名称

### 服务器配置
- `WORKER_ID` - 工作节点ID

### 心跳配置
- `HEARTBEAT_MAIN_SERVER_URL` - 主服务器URL
- `HEARTBEAT_WORKER_HOST` - 工作节点主机地址
- `HEARTBEAT_WORKER_PORT` - 工作节点端口
- `HEARTBEAT_WORKER_NAME` - 工作节点名称
- `HEARTBEAT_MAX_CONCURRENT_TASKS` - 最大并发任务数
- `HEARTBEAT_INTERVAL` - 心跳发送间隔（秒）
- `HEARTBEAT_TIMEOUT` - 心跳请求超时时间（秒）
- `HEARTBEAT_RETRY_COUNT` - 重试次数
- `HEARTBEAT_STATUS` - 默认状态
- `HEARTBEAT_CURRENT_LOAD` - 当前负载

### 预约系统配置
- `SLEEP_TIME` - 请求间隔时间
- `MAX_ATTEMPT` - 最大尝试次数
- `ENABLE_SLIDER` - 是否启用滑块验证
- `RESERVE_NEXT_DAY` - 是否预约明天

### 学校筛选配置
- `SCHOOL_FILTER_ENABLED` - 是否启用学校筛选
- `SCHOOL_FILTER_NAMES` - 学校名称列表（逗号分隔）
- `SCHOOL_FILTER_FALLBACK` - 是否回退到处理所有任务

### 日志配置
- `LOG_LEVEL` - 日志级别

## PowerShell 环境变量设置示例

```powershell
# 数据库配置
$env:DB_HOST = "**************"
$env:DB_PORT = "3306"
$env:DB_USER = "seatmaster"
$env:DB_PASSWORD = "your_password"
$env:DB_DATABASE = "seat_reservation"

# 心跳配置
$env:HEARTBEAT_MAIN_SERVER_URL = "http://**************:8081"
$env:HEARTBEAT_WORKER_HOST = "localhost"
$env:HEARTBEAT_WORKER_PORT = "8082"

# 工作节点ID
$env:WORKER_ID = "worker-8083"
```

## 测试配置

运行以下命令测试配置是否正确：

```bash
python test_db_config.py
```

这将显示：
- 环境变量设置情况
- 配置文件加载情况
- 数据库连接测试结果
- 心跳配置信息
