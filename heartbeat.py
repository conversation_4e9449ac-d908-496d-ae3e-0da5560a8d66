#!/usr/bin/env python3
"""
SeatMaster 心跳客户端
向主服务器发送心跳请求，监控副服务器状态
"""

import os
import sys
import time
import json
import logging
import argparse
import requests
from typing import Dict, Any, Optional
from datetime import datetime
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import config


class HeartbeatClient:
    """心跳客户端类"""
    
    def __init__(self, worker_id: Optional[str] = None, worker_host: Optional[str] = None, worker_port: Optional[int] = None):
        """
        初始化心跳客户端

        Args:
            worker_id: 工作节点ID，如果不提供则从配置中获取
            worker_host: 副服务器主机地址，如果不提供则从配置中获取
            worker_port: 副服务器端口，如果不提供则从配置中获取
        """
        self.logger = self._setup_logger()
        
        # 加载配置
        self.worker_id = worker_id or config.get_worker_id()
        self.main_server_url = config.get_heartbeat_main_server_url()
        self.worker_host = worker_host or config.get_heartbeat_worker_host()
        self.worker_port = worker_port or config.get_heartbeat_worker_port()
        self.worker_name = config.get_heartbeat_worker_name()
        self.max_concurrent_tasks = config.get_heartbeat_max_concurrent_tasks()
        self.supported_operations = config.get_heartbeat_supported_operations()
        self.timeout = config.get_heartbeat_timeout()
        self.retry_count = config.get_heartbeat_retry_count()
        self.status = config.get_heartbeat_status()
        self.current_load = config.get_heartbeat_current_load()
        
        # 验证配置
        self._validate_config()

        # 构建心跳URL
        self.heartbeat_url = f"{self.main_server_url.rstrip('/')}/api/worker/heartbeat/{self.worker_id}"

        # 初始化HTTP会话（连接池复用）
        self.session = self._create_session()

        # 统计信息
        self.stats = {
            'total_attempts': 0,
            'success_count': 0,
            'failure_count': 0,
            'last_success_time': None,
            'last_failure_time': None
        }

        self.logger.info(f"心跳客户端初始化完成 - Worker ID: {self.worker_id}")
        self.logger.info(f"主服务器地址: {self.main_server_url}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置专用日志记录器"""
        logger = logging.getLogger('heartbeat')
        
        # 清除可能存在的旧处理器
        for handler in logger.handlers[:]:
            handler.close()
            logger.removeHandler(handler)
        
        logger.setLevel(logging.INFO)
        logger.propagate = False
        
        # 创建日志目录
        log_dir = config.get('logging', 'log_dir', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # 文件处理器
        log_file = os.path.join(log_dir, 'heartbeat.log')
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def _validate_config(self):
        """验证配置完整性"""
        if not self.worker_id:
            raise ValueError("缺少必需配置: worker_id")
        
        if not self.main_server_url:
            raise ValueError("缺少必需配置: main_server_url")
        
        if self.status not in ['ONLINE', 'OFFLINE', 'BUSY', 'MAINTENANCE']:
            raise ValueError(f"无效的状态值: {self.status}")
        
        self.logger.debug("配置验证通过")

    def _create_session(self) -> requests.Session:
        """创建HTTP会话，配置连接池和重试策略"""
        session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=0,  # 我们自己处理重试
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        # 配置HTTP适配器
        adapter = HTTPAdapter(
            pool_connections=1,
            pool_maxsize=1,
            max_retries=retry_strategy
        )

        session.mount("http://", adapter)
        session.mount("https://", adapter)

        return session
    
    def _build_heartbeat_data(self) -> Dict[str, Any]:
        """构建心跳数据"""
        return {
            "workerId": self.worker_id,
            "status": self.status,
            "currentLoad": self.current_load,
            "timestamp": int(time.time() * 1000),  # 毫秒时间戳
            "host": self.worker_host,
            "port": self.worker_port,
            "name": self.worker_name,
            "maxConcurrentTasks": self.max_concurrent_tasks,
            "supportedOperations": self.supported_operations
        }
    
    def _send_request(self, data: Dict[str, Any]) -> requests.Response:
        """发送HTTP请求"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': f'SeatMaster-Heartbeat-Client/{self.worker_id}'
        }
        
        self.logger.debug(f"发送心跳请求到: {self.heartbeat_url}")
        self.logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False)}")
        
        response = self.session.post(
            url=self.heartbeat_url,
            json=data,
            headers=headers,
            timeout=self.timeout,
            verify=True
        )
        
        return response
    
    def send_heartbeat(self, status: Optional[str] = None, current_load: Optional[int] = None) -> bool:
        """
        发送心跳请求
        
        Args:
            status: 服务器状态，如果不提供则使用配置中的默认值
            current_load: 当前负载，如果不提供则使用配置中的默认值
            
        Returns:
            bool: 发送成功返回True，失败返回False
        """
        # 使用传入的参数或默认值
        if status:
            self.status = status
        if current_load is not None:
            self.current_load = current_load
        
        # 构建心跳数据
        heartbeat_data = self._build_heartbeat_data()

        # 更新统计信息
        self.stats['total_attempts'] += 1

        # 尝试发送请求（包含重试）
        for attempt in range(self.retry_count + 1):
            try:
                self.logger.info(f"发送心跳 (尝试 {attempt + 1}/{self.retry_count + 1})")
                
                response = self._send_request(heartbeat_data)
                
                # 检查HTTP状态码
                if response.status_code == 200:
                    # 解析响应
                    try:
                        result = response.json()
                        self.logger.debug(f"服务器响应: {result}")

                        # 检查响应格式，支持多种成功判断方式
                        is_success = (
                            result.get('success', False) or  # 标准格式
                            result.get('code') == 200 or     # HTTP状态码格式
                            '成功' in str(result.get('message', ''))  # 消息内容判断
                        )

                        if is_success:
                            self.stats['success_count'] += 1
                            self.stats['last_success_time'] = datetime.now()
                            self.logger.info(f"心跳发送成功: {result.get('message', '无消息')}")
                            return True
                        else:
                            self.logger.error(f"心跳发送失败: {result.get('message', '未知错误')}")
                            return False
                    except json.JSONDecodeError as e:
                        self.logger.error(f"响应JSON解析失败: {e}")
                        self.logger.debug(f"响应内容: {response.text}")
                        return False
                else:
                    self.logger.error(f"HTTP请求失败: {response.status_code} - {response.text}")
                    
            except requests.exceptions.Timeout:
                self.logger.warning(f"请求超时 (尝试 {attempt + 1}/{self.retry_count + 1})")
            except requests.exceptions.ConnectionError as e:
                self.logger.warning(f"连接错误 (尝试 {attempt + 1}/{self.retry_count + 1}): {e}")
            except requests.exceptions.RequestException as e:
                self.logger.error(f"请求异常 (尝试 {attempt + 1}/{self.retry_count + 1}): {e}")
            except Exception as e:
                self.logger.error(f"未知错误 (尝试 {attempt + 1}/{self.retry_count + 1}): {e}")
            
            # 如果不是最后一次尝试，等待一段时间再重试
            if attempt < self.retry_count:
                wait_time = 2 ** attempt  # 指数退避
                self.logger.info(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
        
        # 更新失败统计
        self.stats['failure_count'] += 1
        self.stats['last_failure_time'] = datetime.now()

        self.logger.error(f"心跳发送失败，已尝试 {self.retry_count + 1} 次")
        return False

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()

    def close(self):
        """关闭HTTP会话"""
        if hasattr(self, 'session'):
            self.session.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='SeatMaster 心跳客户端',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python heartbeat.py                           # 使用默认配置发送心跳
  python heartbeat.py --worker-id worker-001    # 指定工作节点ID
  python heartbeat.py --status BUSY             # 指定状态
  python heartbeat.py --current-load 5          # 指定当前负载
        """
    )
    
    parser.add_argument(
        '--worker-id', 
        help='工作节点ID（覆盖配置文件中的设置）'
    )
    parser.add_argument(
        '--status', 
        choices=['ONLINE', 'OFFLINE', 'BUSY', 'MAINTENANCE'],
        help='服务器状态'
    )
    parser.add_argument(
        '--current-load',
        type=int,
        help='当前负载（正在处理的任务数）'
    )
    parser.add_argument(
        '--worker-host',
        help='副服务器主机地址'
    )
    parser.add_argument(
        '--worker-port',
        type=int,
        help='副服务器端口'
    )
    parser.add_argument(
        '--config', 
        help='配置文件路径'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='启用详细日志输出'
    )
    parser.add_argument(
        '--stats',
        action='store_true',
        help='显示统计信息'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger('heartbeat').setLevel(logging.DEBUG)
    
    try:
        # 如果指定了配置文件，重新加载配置
        if args.config:
            from config.settings import Config
            global config
            config = Config(args.config)
        
        # 创建心跳客户端
        client = HeartbeatClient(
            worker_id=args.worker_id,
            worker_host=args.worker_host,
            worker_port=args.worker_port
        )
        
        # 发送心跳
        success = client.send_heartbeat(
            status=args.status,
            current_load=args.current_load
        )

        # 显示统计信息
        if args.stats:
            stats = client.get_stats()
            print(f"\n=== 心跳统计信息 ===")
            print(f"总尝试次数: {stats['total_attempts']}")
            print(f"成功次数: {stats['success_count']}")
            print(f"失败次数: {stats['failure_count']}")
            if stats['last_success_time']:
                print(f"最后成功时间: {stats['last_success_time']}")
            if stats['last_failure_time']:
                print(f"最后失败时间: {stats['last_failure_time']}")

        # 清理资源
        client.close()

        # 退出码
        sys.exit(0 if success else 1)
        
    except Exception as e:
        print(f"心跳客户端启动失败: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
