# SeatMaster 心跳脚本使用说明

## 概述

本心跳脚本用于向SeatMaster主服务器发送心跳请求，监控副服务器的运行状态。脚本设计为单次执行模式，适合通过定时任务（如cron）进行调度。

## 功能特性

- ✅ 单次执行设计，适合定时任务调度
- ✅ 从现有配置文件读取配置信息
- ✅ 完善的错误处理和重试机制
- ✅ 详细的日志记录
- ✅ 命令行参数支持
- ✅ 网络异常处理
- ✅ 配置验证

## 文件结构

```
├── heartbeat.py              # 主心跳脚本
├── test_heartbeat.py         # 测试脚本
├── config/settings.py        # 配置文件（已扩展）
├── logs/heartbeat.log        # 心跳日志文件
└── HEARTBEAT_README.md       # 本说明文档
```

## 配置说明

### 新增配置项

在 `config/settings.py` 中新增了 `heartbeat` 配置节：

```python
'heartbeat': {
    'main_server_url': 'http://localhost:8081',  # 主服务器地址
    'worker_host': 'localhost',                  # 副服务器主机地址
    'worker_port': 8082,                         # 副服务器端口
    'worker_name': 'SeatMaster Worker Server',   # 服务器名称
    'max_concurrent_tasks': 10,                  # 最大并发任务数
    'supported_operations': ['XUEXITONG_RESERVATION'],  # 支持的操作
    'interval': 30,                              # 心跳间隔（秒）
    'timeout': 5,                                # 请求超时（秒）
    'retry_count': 1,                            # 重试次数
    'status': 'ONLINE',                          # 默认状态
    'current_load': 0                            # 默认负载
}
```

### 环境变量支持

可通过环境变量覆盖配置：

- `HEARTBEAT_MAIN_SERVER_URL`: 主服务器地址
- `HEARTBEAT_WORKER_HOST`: 副服务器主机地址
- `HEARTBEAT_WORKER_PORT`: 副服务器端口
- `HEARTBEAT_WORKER_NAME`: 服务器名称
- `HEARTBEAT_MAX_CONCURRENT_TASKS`: 最大并发任务数
- `HEARTBEAT_INTERVAL`: 心跳间隔
- `HEARTBEAT_TIMEOUT`: 请求超时时间
- `HEARTBEAT_RETRY_COUNT`: 重试次数
- `HEARTBEAT_STATUS`: 默认状态
- `HEARTBEAT_CURRENT_LOAD`: 默认负载

## 使用方法

### 基本使用

```bash
# 使用默认配置发送心跳
python heartbeat.py

# 查看帮助信息
python heartbeat.py --help

# 指定工作节点ID
python heartbeat.py --worker-id worker-001

# 指定服务器状态
python heartbeat.py --status BUSY

# 指定当前负载
python heartbeat.py --current-load 5

# 指定服务器地址和端口
python heartbeat.py --worker-host ************* --worker-port 8082

# 启用详细日志
python heartbeat.py --verbose

# 显示统计信息
python heartbeat.py --stats
```

### 高级使用

```bash
# 使用自定义配置文件
python heartbeat.py --config /path/to/config.json

# 组合参数使用
python heartbeat.py --worker-id worker-001 --status BUSY --current-load 3 --verbose
```

### 定时任务配置

#### Linux/macOS (cron)

```bash
# 编辑crontab
crontab -e

# 每30秒发送一次心跳
* * * * * /usr/bin/python3 /path/to/heartbeat.py
* * * * * sleep 30; /usr/bin/python3 /path/to/heartbeat.py
```

#### Windows (任务计划程序)

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置触发器为"每30秒"
4. 操作设置为运行 `python heartbeat.py`

## 状态说明

支持的服务器状态：

- `ONLINE`: 在线状态，可以接收新任务
- `OFFLINE`: 离线状态，无法接收任务
- `BUSY`: 繁忙状态，负载较高
- `MAINTENANCE`: 维护状态，暂停服务

## 测试验证

### 运行测试脚本

```bash
# 运行完整测试
python test_heartbeat.py
```

测试内容包括：
- 配置加载测试
- 心跳客户端初始化测试
- 心跳数据构建测试
- 心跳发送准备测试

### 手动测试

```bash
# 测试配置加载
python -c "from config.settings import config; print(config.get_heartbeat_config())"

# 测试心跳客户端
python -c "from heartbeat import HeartbeatClient; client = HeartbeatClient(); print('OK')"
```

## 日志说明

### 日志文件

- 位置: `logs/heartbeat.log`
- 格式: `时间戳 - 日志级别 - 消息内容`
- 编码: UTF-8

### 日志级别

- `INFO`: 正常操作信息
- `WARNING`: 警告信息（如重试）
- `ERROR`: 错误信息
- `DEBUG`: 调试信息（需要 `--verbose` 参数）

### 示例日志

```
2025-07-22 09:00:00,123 - INFO - 心跳客户端初始化完成 - Worker ID: worker-8083
2025-07-22 09:00:00,124 - INFO - 主服务器地址: http://localhost:8081
2025-07-22 09:00:00,125 - INFO - 发送心跳 (尝试 1/2)
2025-07-22 09:00:00,200 - INFO - 心跳发送成功: 心跳更新成功
```

## 错误处理

### 常见错误及解决方案

1. **配置错误**
   ```
   错误: 缺少必需配置: main_server_url
   解决: 检查配置文件或设置环境变量
   ```

2. **网络连接错误**
   ```
   错误: 连接错误: Connection refused
   解决: 检查主服务器是否运行，网络是否通畅
   ```

3. **请求超时**
   ```
   错误: 请求超时
   解决: 增加超时时间或检查网络状况
   ```

4. **认证失败**
   ```
   错误: HTTP请求失败: 401
   解决: 检查worker_id是否正确
   ```

### 重试机制

- 自动重试：网络错误时自动重试
- 指数退避：重试间隔逐渐增加（1秒、2秒、4秒...）
- 重试次数：可配置，默认1次

## API接口说明

### 心跳接口

- **URL**: `POST /api/worker/heartbeat/{worker_id}`
- **Content-Type**: `application/json`
- **认证**: 无需认证

### 请求体格式

```json
{
  "workerId": "worker-001",
  "status": "ONLINE",
  "currentLoad": 3,
  "timestamp": 1719564000000,
  "host": "localhost",
  "port": 8082,
  "name": "SeatMaster Worker Server",
  "maxConcurrentTasks": 10,
  "supportedOperations": ["XUEXITONG_RESERVATION"]
}
```

### 响应格式

成功响应：
```json
{
  "code": 200,
  "message": "心跳更新成功",
  "data": "心跳更新成功",
  "success": true
}
```

错误响应：
```json
{
  "code": 500,
  "message": "心跳更新失败: 具体错误信息",
  "data": null,
  "success": false
}
```

## 故障排查

### 检查清单

1. ✅ 配置文件是否正确
2. ✅ 主服务器是否运行
3. ✅ 网络连接是否正常
4. ✅ worker_id是否正确
5. ✅ 日志文件是否有错误信息

### 调试命令

```bash
# 检查配置
python -c "from config.settings import config; print(config.get_heartbeat_config())"

# 测试网络连接
curl -X POST http://localhost:8081/api/worker/heartbeat/test -H "Content-Type: application/json" -d '{"status":"ONLINE"}'

# 查看详细日志
python heartbeat.py --verbose

# 运行测试
python test_heartbeat.py
```

## 性能说明

- **内存占用**: 约10-20MB
- **执行时间**: 通常1-3秒
- **网络流量**: 每次约200-500字节
- **CPU占用**: 极低

## 版本信息

- **脚本版本**: v1.0
- **兼容系统**: SeatMaster v2.1+
- **Python版本**: 3.6+
- **依赖库**: requests

## 联系支持

如遇问题，请检查：
1. 日志文件 `logs/heartbeat.log`
2. 运行测试脚本 `python test_heartbeat.py`
3. 查看主服务器日志
