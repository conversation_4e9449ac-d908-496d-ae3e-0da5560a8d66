"""
数据访问层 - Repository模式实现
封装数据库操作，提供简洁的数据访问接口
"""
import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, time, timedelta
from .connection import DatabaseManager

class ReservationRepository:
    """预约数据仓库"""

    def __init__(self, db_manager: DatabaseManager):
        """
        初始化预约数据仓库

        Args:
            db_manager: 数据库管理器
        """
        self.db = db_manager
        self.logger = logging.getLogger(__name__)
        self._school_support_checked = False
        self._supports_school_filter = False
        self._join_strategy = None  # 'direct', 'indirect_user', or 'indirect_room'

    def _map_reservation_type_to_db(self, reservation_type: str) -> str:
        """
        将业务层的预约类型映射到数据库存储格式

        Args:
            reservation_type: 业务层预约类型 ('ADVANCE_ONE_DAY', 'SAME_DAY', 'tomorrow', 'today')

        Returns:
            str: 数据库格式的预约类型 ('tomorrow', 'today')
        """
        type_mapping = {
            'ADVANCE_ONE_DAY': 'tomorrow',
            'SAME_DAY': 'today',
            'advance_one_day': 'tomorrow',
            'same_day': 'today',
            'tomorrow': 'tomorrow',
            'today': 'today'
        }

        mapped_type = type_mapping.get(reservation_type, 'today')
        if reservation_type not in type_mapping:
            self.logger.warning(f"未知的预约类型 '{reservation_type}'，使用默认值 'today'")

        return mapped_type

    def _map_reservation_type_from_db(self, db_reservation_type: str) -> str:
        """
        将数据库的预约类型映射到业务层格式

        支持多种输入格式以保持向后兼容性：
        - 数据库格式：'tomorrow', 'today'
        - 业务层格式：'ADVANCE_ONE_DAY', 'SAME_DAY'
        - 其他格式：'advance_one_day', 'same_day'

        Args:
            db_reservation_type: 预约类型（支持多种格式）

        Returns:
            str: 业务层格式的预约类型 ('ADVANCE_ONE_DAY', 'SAME_DAY')
        """
        type_mapping = {
            # 数据库格式 -> 业务层格式
            'tomorrow': 'ADVANCE_ONE_DAY',
            'today': 'SAME_DAY',
            # 向后兼容：业务层格式 -> 业务层格式
            'ADVANCE_ONE_DAY': 'ADVANCE_ONE_DAY',
            'SAME_DAY': 'SAME_DAY',
            # 其他可能的格式
            'advance_one_day': 'ADVANCE_ONE_DAY',
            'same_day': 'SAME_DAY'
        }

        mapped_type = type_mapping.get(db_reservation_type, 'SAME_DAY')
        if db_reservation_type not in type_mapping:
            self.logger.warning(f"未知的预约类型 '{db_reservation_type}'，使用默认值 'SAME_DAY'")

        return mapped_type

    def _validate_reservation_type(self, reservation_type: str) -> bool:
        """
        验证预约类型是否有效

        Args:
            reservation_type: 预约类型

        Returns:
            bool: 是否有效
        """
        valid_types = {
            'ADVANCE_ONE_DAY', 'SAME_DAY', 'advance_one_day', 'same_day',
            'tomorrow', 'today'
        }
        return reservation_type in valid_types

    def _check_school_support(self) -> bool:
        """
        检查数据库是否支持学校筛选功能，并确定关联策略。
        策略1 (direct): reservations.school_id -> schools.id
        策略2 (indirect_user): reservations.user_id -> users.id -> users.school_id -> schools.id
        策略3 (indirect_room): reservations.room_id -> rooms.id -> rooms.school_id -> schools.id

        Returns:
            bool: 是否支持学校筛选
        """
        if self._school_support_checked:
            return self._supports_school_filter

        self._school_support_checked = True

        try:
            # 1. 检查 schools 表是否存在
            try:
                self.db.execute_query("DESCRIBE schools")
                self.logger.debug("检测到 schools 表存在")
            except Exception:
                self.logger.debug("schools 表不存在，不支持学校筛选")
                self._supports_school_filter = False
                return False

            # 2. 尝试策略1: 直接关联 (reservations.school_id)
            try:
                if 'school_id' in [c['Field'] for c in self.db.execute_query("DESCRIBE reservations")]:
                    self.logger.info("检测到 reservations.school_id，采用[直接关联]策略")
                    self._supports_school_filter = True
                    self._join_strategy = 'direct'
                    return True
            except Exception: pass

            # 3. 尝试策略3: 间接通过房间关联 (rooms.school_id)
            try:
                if 'school_id' in [c['Field'] for c in self.db.execute_query("DESCRIBE rooms")]:
                    self.logger.info("检测到 rooms.school_id，采用[房间间接关联]策略")
                    self._supports_school_filter = True
                    self._join_strategy = 'indirect_room'
                    return True
            except Exception: pass

            # 4. 尝试策略2: 间接通过用户关联 (users.school_id)
            try:
                if 'school_id' in [c['Field'] for c in self.db.execute_query("DESCRIBE users")]:
                    self.logger.info("检测到 users.school_id，采用[用户间接关联]策略")
                    self._supports_school_filter = True
                    self._join_strategy = 'indirect_user'
                    return True
            except Exception: pass

            self.logger.warning("数据库不支持学校筛选：未找到任何有效的学校关联字段")
            self._supports_school_filter = False
            return False

        except Exception as e:
            self.logger.error(f"检查数据库学校支持时出错: {e}")
            self._supports_school_filter = False
            return False

    def get_reservations_batch(self, worker_id: str, school_names: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        通过单次批量查询加载所有预约任务及其关联数据（支持动态列名）
        """
        self.logger.info(f"开始批量加载预约任务 (Worker: {worker_id}, 学校: {school_names or '所有'})")

        try:
            # 动态探测 schools 表的列名
            school_name_col = "s.school_name"
            school_code_col = "s.school_code"
            has_school_code = False

            try:
                school_columns_info = self.db.execute_query("DESCRIBE schools")
                school_available_columns = [col['Field'] for col in school_columns_info]
                self.logger.debug(f"检测到 schools 表字段: {school_available_columns}")

                # 检查 school_name 字段
                if 'school_name' not in school_available_columns and 'name' in school_available_columns:
                    school_name_col = "s.name"
                    self.logger.debug("使用 s.name 作为学校名称字段")

                # 检查 school_code 字段
                if 'school_code' in school_available_columns:
                    school_code_col = "s.school_code"
                    has_school_code = True
                elif 'code' in school_available_columns:
                    school_code_col = "s.code"
                    has_school_code = True
                else:
                    self.logger.warning("schools 表中没有找到 school_code 或 code 字段，将使用学校名称作为代码")
                    has_school_code = False

            except Exception as e:
                self.logger.warning(f"无法探测 'schools' 表结构: {e}，将使用默认列名。")

            # 构建 SQL 查询，根据字段存在情况调整
            if has_school_code:
                query = f"""
                SELECT
                    r.id AS reservation_id, r.seat_num, r.start_time, r.end_time,
                    r.reservation_open_time, r.reservation_type, r.max_retry_count,
                    u.username, u.password,
                    rm.roomNum AS room_num, rm.max_reservation_hours, rm.captcha AS enable_captcha,
                    s.id AS school_id, {school_name_col} AS school_name, {school_code_col} AS school_code, s.wait_time, s.seldom
                FROM
                    reservations r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN rooms rm ON r.room_id = rm.id
                LEFT JOIN schools s ON rm.school_id = s.id
                WHERE r.worker_id = %s
                """
            else:
                query = f"""
                SELECT
                    r.id AS reservation_id, r.seat_num, r.start_time, r.end_time,
                    r.reservation_open_time, r.reservation_type, r.max_retry_count,
                    u.username, u.password,
                    rm.roomNum AS room_num, rm.max_reservation_hours, rm.captcha AS enable_captcha,
                    s.id AS school_id, {school_name_col} AS school_name, {school_name_col} AS school_code, s.wait_time, s.seldom
                FROM
                    reservations r
                LEFT JOIN users u ON r.user_id = u.id
                LEFT JOIN rooms rm ON r.room_id = rm.id
                LEFT JOIN schools s ON rm.school_id = s.id
                WHERE r.worker_id = %s
                """

            params = [worker_id]

            if school_names:
                query += f" AND {school_name_col} IN ({','.join(['%s'] * len(school_names))})"
                params.extend(school_names)

            self.logger.debug(f"执行查询: {query}")
            self.logger.debug(f"查询参数: {params}")
            results = self.db.execute_query(query, tuple(params))
            self.logger.info(f"从数据库加载了 {len(results)} 条原始预约记录")
            
            reservations = [self._convert_db_row_to_config(row) for row in results]
            valid_reservations = [res for res in reservations if res is not None]
            
            self.logger.info(f"成功转换 {len(valid_reservations)} 条有效的预约任务")
            return valid_reservations
            
        except Exception as e:
            # 捕获并记录具体的数据库错误
            if "Unknown column" in str(e):
                self.logger.error(f"数据库操作异常: {e}")
            self.logger.error(f"批量加载预约任务失败: {e}", exc_info=True)
            return []
    
    def _convert_db_row_to_config(self, row: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        将批量查询的数据库行转换为统一的预约任务数据结构
        """
        # 安全地从行数据中提取信息
        def safe_get(key, default=None):
            return row.get(key, default)

        # 检查核心字段是否存在
        reservation_id = safe_get('reservation_id')
        username = safe_get('username')
        password = safe_get('password')
        room_num = safe_get('room_num')
        seat_num = safe_get('seat_num')

        if not all([reservation_id, username, password, room_num, seat_num]):
            self.logger.warning(f"跳过无效记录 (ID: {reservation_id}): 缺少核心信息")
            return None

        # 处理时间
        start_time = str(safe_get('start_time', '08:00:00'))
        end_time = str(safe_get('end_time', '18:00:00'))
        max_hours = safe_get('max_reservation_hours', 4)
        time_slots = self._split_time_if_needed(start_time, end_time, max_hours)

        # 处理座位号
        seatid = [f"{int(seat_num):03d}"] if str(seat_num).isdigit() else [str(seat_num)]

        # 处理布尔值
        enable_captcha = str(safe_get('enable_captcha')).lower() in ('1', 'true', 'yes', 'on')

        # 处理预约类型 - 从数据库格式映射到业务层格式
        db_reservation_type = safe_get('reservation_type', 'today')
        business_reservation_type = self._map_reservation_type_from_db(db_reservation_type)

        # 验证预约类型
        if not self._validate_reservation_type(business_reservation_type):
            self.logger.warning(f"预约记录 {reservation_id} 的预约类型无效: {business_reservation_type}，使用默认值 SAME_DAY")
            business_reservation_type = 'SAME_DAY'

        self.logger.debug(f"预约记录 {reservation_id}: 数据库类型 '{db_reservation_type}' -> 业务类型 '{business_reservation_type}'")

        # 构建学校信息
        school_info = {
            'school_id': safe_get('school_id'),
            'school_name': safe_get('school_name'),
            'school_code': safe_get('school_code'),
            'wait_time': float(safe_get('wait_time', 0.5)),
            'seldom': int(safe_get('seldom', 0))
        }
        school_info = {k: v for k, v in school_info.items() if v is not None}

        # 构建注释
        comment_parts = [f"学校{school_info.get('school_name', '未知')}", f"用户{username}", f"房间{room_num}", f"座位{seat_num}"]

        # 返回完整的数据结构
        return {
            'id': reservation_id,
            'username': username,
            'password': password,
            'roomid': room_num,
            'seatid': seatid,
            'time_slots': time_slots,
            'daysofweek': ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
            'open_reserve_time': str(safe_get('reservation_open_time', '07:00:00')),
            'reservation_type': business_reservation_type,
            'max_attempt': safe_get('max_retry_count', 3),
            'enable_slider': enable_captcha,
            'school_info': school_info,
            'wait_time': school_info.get('wait_time', 0.5),
            '_comment': "_".join(comment_parts)
        }

    
    def _normalize_time_format(self, time_str: str) -> str:
        """
        标准化时间格式为HH:MM:SS

        Args:
            time_str: 输入时间字符串，支持格式：HH:MM, HH:MM:SS, H:MM等

        Returns:
            str: 标准化后的HH:MM:SS格式时间字符串
        """
        if not time_str:
            return "00:00:00"

        # 移除可能的空白字符和尾随冒号
        time_str = time_str.strip().rstrip(':')

        try:
            # 尝试解析不同的时间格式
            if ':' not in time_str:
                # 只有小时，如 "9" -> "09:00:00"
                hour = int(time_str)
                return f"{hour:02d}:00:00"

            parts = time_str.split(':')

            if len(parts) == 2:
                # HH:MM格式，补充秒
                hour, minute = int(parts[0]), int(parts[1])
                return f"{hour:02d}:{minute:02d}:00"
            elif len(parts) == 3:
                # HH:MM:SS格式，验证并标准化
                hour, minute, second = int(parts[0]), int(parts[1]), int(parts[2])
                return f"{hour:02d}:{minute:02d}:{second:02d}"
            else:
                self.logger.warning(f"无法解析时间格式: {time_str}，使用默认值")
                return "00:00:00"

        except (ValueError, IndexError) as e:
            self.logger.error(f"时间格式解析错误: {time_str}, 错误: {e}")
            return "00:00:00"

    def _split_time_if_needed(self, start_time: str, end_time: str, max_hours: int) -> List[List[str]]:
        """
        如果时间段超过最大小时数，则拆分时间段
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            max_hours: 最大小时数
            
        Returns:
            List[List[str]]: 时间段列表
        """
        try:
            # 确保max_hours是数值类型
            max_hours = float(max_hours)

            # 标准化输入时间格式
            normalized_start = self._normalize_time_format(start_time)
            normalized_end = self._normalize_time_format(end_time)

            start_dt = datetime.strptime(normalized_start, '%H:%M:%S')
            end_dt = datetime.strptime(normalized_end, '%H:%M:%S')
            
            # 如果结束时间小于开始时间，说明跨天了
            if end_dt < start_dt:
                end_dt += timedelta(days=1)
            
            duration = end_dt - start_dt
            duration_hours = duration.total_seconds() / 3600
            
            if duration_hours <= max_hours:
                # 返回HH:MM:SS格式（已经标准化过）
                return [[normalized_start, normalized_end]]
            
            # 需要拆分
            time_slots = []
            current_start = start_dt
            
            while current_start < end_dt:
                current_end = current_start + timedelta(hours=max_hours)
                if current_end > end_dt:
                    current_end = end_dt
                
                slot_start = current_start.strftime('%H:%M:%S')
                slot_end = current_end.strftime('%H:%M:%S')
                time_slots.append([slot_start, slot_end])
                
                current_start = current_end
            
            self.logger.info(f"时间段拆分: {start_time}-{end_time} -> {len(time_slots)} 个时间段")
            return time_slots
            
        except Exception as e:
            self.logger.error(f"时间段拆分失败: {e}")
            # 异常情况下也返回标准化的HH:MM:SS格式
            fallback_start = self._normalize_time_format(start_time)
            fallback_end = self._normalize_time_format(end_time)
            return [[fallback_start, fallback_end]]
    
    def batch_save_results(self, results: List[Dict[str, Any]]):
        """
        批量将预约结果写入数据库
        """
        if not results:
            return

        self.logger.info(f"开始批量写入 {len(results)} 条预约结果...")

        # 预处理结果，将 api_response 字典转换为 JSON 字符串，并验证时间格式
        processed_results = []
        for result in results:
            processed_result = result.copy()

            # 验证和标准化时间格式
            if 'start_time' in processed_result:
                original_start = processed_result['start_time']
                processed_result['start_time'] = self._normalize_time_format(str(original_start))
                if original_start != processed_result['start_time']:
                    self.logger.debug(f"标准化start_time: '{original_start}' -> '{processed_result['start_time']}'")

            if 'end_time' in processed_result:
                original_end = processed_result['end_time']
                processed_result['end_time'] = self._normalize_time_format(str(original_end))
                if original_end != processed_result['end_time']:
                    self.logger.debug(f"标准化end_time: '{original_end}' -> '{processed_result['end_time']}'")

            # 处理 api_response
            if 'api_response' in processed_result and isinstance(processed_result['api_response'], dict):
                processed_result['api_response'] = json.dumps(processed_result['api_response'], ensure_ascii=False)

            processed_results.append(processed_result)

        sql = """
        INSERT INTO reservation_logs
        (reservation_id, username, roomid, seatid, reserve_date, start_time, end_time,
         status, error_message, api_response, api_response_time, attempt_count, execution_time)
        VALUES (%(reservation_id)s, %(username)s, %(roomid)s, %(seatid)s, %(reserve_date)s,
                %(start_time)s, %(end_time)s, %(status)s, %(error_message)s, %(api_response)s,
                %(api_response_time)s, %(attempt_count)s, %(execution_time)s)
        """

        try:
            self.db.execute_many(sql, processed_results)
            self.logger.info(f"成功批量写入 {len(results)} 条预约结果")
        except Exception as e:
            self.logger.error(f"批量写入预约结果失败: {e}", exc_info=True)
    
    def get_reservation_stats(self, worker_id: str, days: int = 7) -> Dict[str, Any]:
        """
        获取预约统计信息

        Args:
            worker_id: 工作节点ID
            days: 统计天数

        Returns:
            Dict: 统计信息
        """
        try:
            # 检查表是否存在
            tables = self.db.execute_query("SHOW TABLES LIKE 'reservation_logs'")
            if not tables:
                self.logger.debug("reservation_logs表不存在，返回空统计信息")
                return {}

            sql = """
            SELECT
                COUNT(*) as total_attempts,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_count,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
                SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as error_count,
                AVG(execution_time) as avg_execution_time
            FROM reservation_logs rl
            JOIN reservations r ON rl.reservation_id = r.id
            WHERE r.worker_id = %s AND rl.created_at >= DATE_SUB(NOW(), INTERVAL %s DAY)
            """

            result = self.db.execute_query(sql, (worker_id, days))
            if result:
                stats = result[0]
                stats['success_rate'] = (
                    stats['success_count'] / stats['total_attempts'] * 100
                    if stats['total_attempts'] > 0 else 0
                )
                return stats
            return {}

        except Exception as e:
            self.logger.warning(f"获取预约统计失败: {e}")
            return {}
    
    def update_reservation_status(self, reservation_id: int, is_active: bool):
        """
        更新预约配置状态
        
        Args:
            reservation_id: 预约配置ID
            is_active: 是否激活
        """
        sql = "UPDATE reservations SET is_active = %s WHERE id = %s"
        
        try:
            affected_rows = self.db.execute_update(sql, (is_active, reservation_id))
            if affected_rows > 0:
                self.logger.info(f"预约配置状态更新成功: ID={reservation_id}, active={is_active}")
            else:
                self.logger.warning(f"预约配置不存在: ID={reservation_id}")
                
        except Exception as e:
            self.logger.error(f"更新预约配置状态失败: {e}")

    def get_school_wait_time(self, school_id: int) -> float:
        """
        根据学校ID获取服务器响应等待时间

        Args:
            school_id: 学校ID

        Returns:
            float: 等待时间（秒），如果未找到则返回默认值
        """
        if not school_id:
            return 0.5  # 默认等待时间

        try:
            # 检查 schools 表是否存在 wait_time 字段
            try:
                columns_info = self.db.execute_query("DESCRIBE schools")
                available_columns = [col['Field'] for col in columns_info]
                has_wait_time_field = 'wait_time' in available_columns
            except Exception:
                has_wait_time_field = False
                self.logger.debug("无法检查schools表结构，假设没有wait_time字段")

            if not has_wait_time_field:
                self.logger.debug(f"学校ID {school_id} 的 schools 表中无 wait_time 字段，使用默认等待时间 0.5s")
                return 0.5

            sql = "SELECT wait_time FROM schools WHERE id = %s"
            result = self.db.execute_query(sql, (school_id,))

            if result and result[0].get('wait_time') is not None:
                wait_time = float(result[0]['wait_time'])
                self.logger.info(f"获取到学校ID {school_id} 的特定等待时间: {wait_time}s")
                return wait_time
            else:
                self.logger.warning(f"未找到学校ID {school_id} 的等待时间配置，使用默认值 0.5s")
                return 0.5

        except Exception as e:
            self.logger.error(f"获取学校 {school_id} 等待时间失败: {e}，使用默认值 0.5s")
            return 0.5

    def get_school_seldom_config(self, school_id: int) -> int:
        """
        根据学校ID获取是否启用随机等待配置

        Args:
            school_id: 学校ID

        Returns:
            int: 0表示禁用，1表示启用，如果未找到则返回默认值0
        """
        if not school_id:
            return 0  # 默认禁用

        try:
            # 检查 schools 表是否存在 seldom 字段
            try:
                columns_info = self.db.execute_query("DESCRIBE schools")
                available_columns = [col['Field'] for col in columns_info]
                has_seldom_field = 'seldom' in available_columns
            except Exception:
                has_seldom_field = False
                self.logger.debug("无法检查schools表结构，假设没有seldom字段")

            if not has_seldom_field:
                self.logger.debug(f"学校ID {school_id} 的 schools 表中无 seldom 字段，随机等待功能未启用")
                return 0

            sql = "SELECT seldom FROM schools WHERE id = %s"
            result = self.db.execute_query(sql, (school_id,))

            if result and result[0].get('seldom') is not None:
                seldom = int(result[0]['seldom'])
                self.logger.info(f"获取到学校ID {school_id} 的随机等待配置: {'启用' if seldom == 1 else '禁用'}")
                return seldom
            else:
                self.logger.debug(f"未找到学校ID {school_id} 的随机等待配置，使用默认值（禁用）")
                return 0

        except Exception as e:
            self.logger.error(f"获取学校 {school_id} 随机等待配置失败: {e}，使用默认值（禁用）")
            return 0
