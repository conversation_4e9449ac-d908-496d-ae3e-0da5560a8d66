# 增强日志记录功能说明

## 概述

本次更新为预约提交过程添加了详细的日志记录功能，帮助排查预约提交过程中可能存在的问题，特别是验证预约类型映射逻辑是否正确工作。

## 修改的文件

1. **xuexitong_pro/utils/reserve.py** - 增强了 `get_submit` 方法的日志记录
2. **utils/reserve.py** - 增强了 `get_submit` 方法的日志记录

## 新增的日志信息

### 1. 预约提交详细信息
- **时间戳**: 精确到毫秒的提交开始时间
- **学校信息**: 学校名称（仅 utils/reserve.py）
- **分隔线**: 清晰的视觉分隔

```
============================================================
预约提交详细信息 - 时间戳: 2024-07-24 14:30:25.123
学校: 测试学校
============================================================
```

### 2. 预约类型分析
详细记录预约类型相关的所有参数和计算过程：

```json
=== 预约类型分析 ===
预约类型详情: {
  "reserve_next_day": false,
  "action": false,
  "delta_day": 0,
  "base_date": "2024-07-24",
  "calculated_date": "2024-07-24",
  "school_name": "测试学校",
  "wait_time": 0.5,
  "school_seldom": 0
}
```

**关键字段说明**:
- `reserve_next_day`: 是否预约明天
- `action`: 是否为 action 模式
- `delta_day`: 日期偏移量
- `base_date`: 基础日期
- `calculated_date`: 计算后的预约日期
- `action_adjusted_date`: action 模式调整后的日期（如果适用）

### 3. 预约参数详情
记录所有提交参数的完整信息：

```json
=== 预约参数详情 ===
原始参数（加密前）: {
  "roomId": "4219",
  "startTime": "08:00:00",
  "endTime": "18:00:00",
  "day": "2024-07-24",
  "seatNum": "380",
  "captcha": "test_captcha",
  "token": "test_token_123456"
}
生成的enc签名: abc123def456...
完整参数（包含签名）: {
  "roomId": "4219",
  "startTime": "08:00:00",
  "endTime": "18:00:00",
  "day": "2024-07-24",
  "seatNum": "380",
  "captcha": "test_captcha",
  "token": "test_token_123456",
  "enc": "abc123def456..."
}
```

### 4. 提交URL构建
记录完整的URL构建过程：

```
=== 提交URL构建 ===
基础URL: https://office.chaoxing.com/data/apps/seat/submit
参数字符串: roomId=4219&startTime=08%3A00%3A00&endTime=18%3A00%3A00...
完整提交URL: https://office.chaoxing.com/data/apps/seat/submit?roomId=4219&...
URL总长度: 256 字符
```

### 5. 参数完整性检查
验证所有必要参数是否完整：

```
=== 参数完整性检查 ===
所有必要参数完整: ✓
验证码状态: 已提供 (长度: 12)
```

### 6. HTTP请求详情
记录请求发送和响应接收的详细时间信息：

```
=== HTTP请求发送 ===
请求发送时间: 2024-07-24 14:30:25.456
响应接收时间: 2024-07-24 14:30:25.678
请求耗时: 222.00 毫秒
```

### 7. 响应解析和结果分析
详细分析服务器响应：

```json
=== 响应解析 ===
原始响应内容: {"success":true,"message":"预约成功"}
解析后响应: {
  "success": true,
  "message": "预约成功",
  "_response_time": "2024-07-24 14:30:25.678"
}

=== 预约结果分析 ===
✓ 预约成功!
成功消息: 预约成功
============================================================
预约提交完成 - 结果: 成功
============================================================
```

**失败情况分析**:
```
✗ 预约失败!
失败原因: 该时间段已被预约
失败类型: 重复预约
```

## 特殊功能

### 1. 错误重试记录
当请求失败时，详细记录重试过程：

```
=== 请求失败 - 第一次尝试 ===
连接错误或超时: Connection timeout
等待2秒后进行重试...

=== 重试请求发送 ===
重试时间: 2024-07-24 14:30:27.789
重试响应时间: 2024-07-24 14:30:28.012
重试耗时: 223.00 毫秒
```

### 2. 定时提交记录
记录定时提交的等待过程：

```
=== 定时提交等待 ===
等待直到指定时间 08:00:00 再进行提交...
到达指定时间 08:00:00，开始提交预约请求...
```

### 3. 学校特定等待策略（仅 utils/reserve.py）
记录学校特定的等待逻辑：

```
=== 学校特定等待策略 ===
根据学校 [测试学校] 设置，额外等待 0.5 秒...
学校等待结束，继续执行...
学校 [测试学校] 启用了随机等待，将额外等待 1.23 秒...
随机等待结束，准备发送预约请求...
```

## 使用方法

1. **运行现有的预约程序**，日志会自动输出到控制台和日志文件
2. **查看日志文件**，寻找以下关键信息：
   - 预约类型分析部分，验证 `reserve_next_day` 和 `action` 参数
   - 日期计算是否正确（`calculated_date` 字段）
   - 参数完整性检查结果
   - 请求和响应的详细时间信息
   - 失败原因分析

## 排查重点

### 1. 预约类型映射验证
检查日志中的预约类型分析部分：
- `reserve_next_day` 值是否符合预期
- `calculated_date` 是否为正确的预约日期
- 如果是 action 模式，检查 `action_adjusted_date`

### 2. 参数完整性
确认所有必要参数都已正确设置：
- roomId, startTime, endTime, day, seatNum, token, enc

### 3. 时间分析
通过请求耗时判断网络状况：
- 正常情况下应在 100-500 毫秒之间
- 超过 1000 毫秒可能存在网络问题

### 4. 失败原因分析
根据失败类型采取相应措施：
- 重复预约：检查是否已有预约
- 验证码问题：检查验证码获取逻辑
- 座位问题：检查座位号是否正确
- 时间问题：检查时间段设置

## 测试建议

1. 运行 `test_enhanced_logging.py` 进行基础测试
2. 在实际预约场景中观察完整的日志输出
3. 特别关注预约类型相关的日志，验证修复的映射逻辑是否正确工作
4. 对比成功和失败的预约请求，分析差异

## 注意事项

1. 日志中可能包含敏感信息（如 token），请注意保护
2. 详细日志会增加输出量，建议在排查问题时启用
3. 生产环境中可以根据需要调整日志级别
4. 建议定期清理日志文件以节省存储空间
