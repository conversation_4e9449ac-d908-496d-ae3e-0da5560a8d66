-- 添加 seldom 字段到 schools 表的迁移脚本
-- 用于实现基于学校配置的随机等待功能

-- 检查 schools 表是否已有 seldom 字段
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'schools'
    AND COLUMN_NAME = 'seldom'
);

-- 如果 seldom 字段不存在，则添加
SET @sql = IF(@column_exists = 0,
    'ALTER TABLE schools ADD COLUMN seldom TINYINT DEFAULT 0 COMMENT "是否启用随机等待(0:禁用 1:启用)" AFTER wait_time',
    'SELECT "seldom 字段已存在" AS message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 显示迁移结果
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'schools'
AND COLUMN_NAME IN ('wait_time', 'seldom')
ORDER BY ORDINAL_POSITION;

-- 显示当前所有学校的配置
SELECT 
    school_code,
    school_name,
    wait_time,
    CASE 
        WHEN seldom IS NULL THEN 'NULL'
        WHEN seldom = 0 THEN '禁用'
        WHEN seldom = 1 THEN '启用'
        ELSE '未知'
    END AS random_wait_status
FROM schools
ORDER BY school_code;