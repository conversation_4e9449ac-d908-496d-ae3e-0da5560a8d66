"""
MySQL数据库版本的座位预约主程序
支持从数据库读取配置，多服务器部署，宝塔cron定时任务
"""
import json
import time
import argparse
import os
import sys
import re
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils import reserve
from database import DatabaseManager, ReservationRepository
from config import config

# 时间工具函数
def get_current_time(action):
    """获取当前时间，action为True时考虑时区偏移"""
    return time.strftime("%H:%M:%S", time.localtime(time.time() + 8*3600)) if action else time.strftime("%H:%M:%S", time.localtime(time.time()))

def get_current_dayofweek(action):
    """获取当前星期几，action为True时考虑时区偏移"""
    return time.strftime("%A", time.localtime(time.time() + 8*3600)) if action else time.strftime("%A", time.localtime(time.time()))

class DatabaseReservationManager:
    """数据库版本的预约管理器"""
    
    def __init__(self):
        """初始化预约管理器"""
        self.logger = logging.getLogger(__name__)
        self.db_manager = None
        self.repository = None
        self.worker_id = config.get_worker_id()
        self.school_names = None  # 要筛选的学校名称列表
        
        # 预约配置
        reservation_config = config.get_reservation_config()
        self.sleep_time = reservation_config.get('sleep_time', 0.2)
        self.max_attempt = reservation_config.get('max_attempt', 3)
        self.enable_slider = reservation_config.get('enable_slider', False)
        self.reserve_next_day = reservation_config.get('reserve_next_day', True)
        self.max_workers = reservation_config.get('max_workers', 50)
        
        # 结果记录
        self.reserve_results = []
        self.results_lock = threading.Lock()
    
    def initialize_database(self) -> bool:
        """初始化数据库连接"""
        try:
            # 如果已经有连接且连接有效，直接返回成功
            if self.db_manager and self.db_manager.is_connected():
                self.logger.debug("数据库连接已存在且有效，跳过初始化")
                return True

            db_config = config.get_database_config()
            self.db_manager = DatabaseManager(**db_config)

            if not self.db_manager.connect():
                self.logger.error("数据库连接失败")
                return False

            if not self.db_manager.test_connection():
                self.logger.error("数据库连接测试失败")
                return False

            self.repository = ReservationRepository(self.db_manager)
            self.logger.info(f"数据库初始化成功 (worker_id: {self.worker_id})")
            return True

        except Exception as e:
            self.logger.error(f"数据库初始化异常: {e}", exc_info=True)
            return False
    
    def cleanup_database(self):
        """清理数据库连接"""
        if self.db_manager:
            self.db_manager.disconnect()
            self.logger.info("数据库连接已清理")
    
    def set_school_filter(self, school_names: List[str] = None):
        """
        设置学校筛选条件

        Args:
            school_names: 要筛选的学校名称列表，None表示不筛选
        """
        self.school_names = school_names
        if school_names:
            self.logger.info(f"设置学校筛选: {school_names}")
        else:
            self.logger.info("未设置学校筛选，将处理所有学校的任务")

    def load_reservations_from_database(self) -> List[Dict[str, Any]]:
        """使用批量查询从数据库加载和预处理所有预约任务"""
        try:
            if self.school_names is None and config.is_school_filter_enabled():
                self.school_names = config.get_school_names()
                self.logger.info(f"从配置加载学校筛选: {self.school_names}")

            reservations = self.repository.get_reservations_batch(self.worker_id, self.school_names)

            if not reservations:
                self.logger.warning(f"未找到任何有效的预约任务 (Worker: {self.worker_id})")
                return []

            self.logger.info(f"成功加载并预处理 {len(reservations)} 个预约任务")
            for i, res in enumerate(reservations):
                self.logger.info(f"  - 任务 {i+1}: {res['_comment']} | 等待时间: {res['wait_time']}s")
            
            return reservations

        except Exception as e:
            self.logger.error(f"从数据库加载预约配置失败: {e}", exc_info=True)
            return []




    def _create_task_logger(self, reservation: Dict[str, Any]) -> logging.Logger:
        """
        为预约任务创建专用的日志记录器

        Args:
            reservation: 预约配置信息

        Returns:
            logging.Logger: 任务专用的日志记录器
        """
        # 获取任务信息
        school_info = reservation.get('school_info', {})
        school_name = school_info.get('school_name', 'Unknown')
        reservation_id = reservation.get('id', 'unknown')
        date_str = datetime.now().strftime('%Y-%m-%d')

        # 清理学校名称中的特殊字符，确保文件名安全
        safe_school_name = re.sub(r'[^\w\-_\u4e00-\u9fff]', '_', school_name)

        # 生成日志文件名：学校_reservations.id_日期.log
        log_filename = f"{safe_school_name}_{reservation_id}_{date_str}.log"

        # 创建日志目录
        log_dir = config.get('logging', 'log_dir', 'logs')
        os.makedirs(log_dir, exist_ok=True)

        # 创建专用日志记录器，使用唯一名称避免冲突
        logger_name = f"task_{reservation_id}_{threading.current_thread().ident}"
        task_logger = logging.getLogger(logger_name)

        # 清除可能存在的旧处理器
        for handler in task_logger.handlers[:]:
            handler.close()
            task_logger.removeHandler(handler)

        # 设置日志级别
        task_logger.setLevel(logging.INFO)
        task_logger.propagate = False  # 防止传播到父日志记录器

        # 创建文件处理器
        log_path = os.path.join(log_dir, log_filename)
        file_handler = logging.FileHandler(log_path, encoding='utf-8')

        # 设置日志格式
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        # 添加处理器
        task_logger.addHandler(file_handler)

        return task_logger

    def _reserve_seat_task(self, reservation: Dict[str, Any], time_slot: List[str],
                           seatid: str, action: bool, target_time: str) -> bool:
        """
        执行单个预约任务的函数，适用于线程池。
        返回 True 表示成功，False 表示失败。
        """
        task_logger = self._create_task_logger(reservation)
        comment = reservation['_comment']
        start_time = datetime.now()

        suc = False
        status = 'failed'
        error_message = None
        attempt_count = 0
        api_response = {}
        api_response_time = None

        try:
            task_logger.info(f"----------- {comment} -- {time_slot} -- {seatid} 尝试预约 -----------")

            wait_time = reservation.get('wait_time', 0.5)
            school_info = reservation.get('school_info', {})
            school_name = school_info.get('school_name', 'Unknown')
            school_seldom = school_info.get('seldom', 0)
            task_logger.info(f"根据学校 [{school_name}] 设置，额外等待 {wait_time} 秒...")

            # 处理预约类型逻辑 - 直接使用数据库的预约类型，避免不必要的转换
            reservation_type = reservation.get('reservation_type', 'SAME_DAY')
            task_logger.info(f"预约类型: {reservation_type}")

            # 参数验证
            required_fields = ['username', 'password', 'roomid']
            missing_fields = [field for field in required_fields if not reservation.get(field)]
            if missing_fields:
                error_message = f"缺少必要参数: {missing_fields}"
                task_logger.error(error_message)
                raise ValueError(error_message)

            # 创建预约实例
            s = reserve(
                sleep_time=self.sleep_time,
                max_attempt=reservation.get('max_attempt', self.max_attempt),
                enable_slider=reservation.get('enable_slider', self.enable_slider),
                reservation_type=reservation_type,  # 直接传递预约类型，不再转换
                logger=task_logger
            )
            attempt_count = s.max_attempt

            # 登录处理
            task_logger.info("开始登录流程...")
            s.get_login_status()
            login_result = s.login(reservation['username'], reservation['password'])

            if isinstance(login_result, tuple) and not login_result[0]:
                error_message = f"登录失败: {login_result[1]}"
                task_logger.error(error_message)
                raise Exception(error_message)

            s.requests.headers.update({'Host': 'office.chaoxing.com'})
            task_logger.info("登录成功，开始提交预约...")

            # 提交预约
            response = s.submit(
                time_slot, reservation['roomid'], [seatid], action,
                submit_time=target_time,
                wait_time=wait_time,
                school_name=school_name,
                school_seldom=school_seldom
            )

            if isinstance(response, dict):
                suc = response.get("success", False)
                api_response = response.copy()

                # 优先使用首次响应时间，如果没有则使用普通响应时间
                api_response_time = response.get("_first_response_time") or response.get("_response_time")

                # 从 api_response 中移除内部字段，保持数据库存储的干净
                if "_response_time" in api_response:
                    del api_response["_response_time"]
                if "_first_response_time" in api_response:
                    del api_response["_first_response_time"]

                if not suc:
                    error_message = response.get("msg", "预约失败")
            else:
                suc = bool(response)
                api_response = {"success": suc, "msg": "预约成功" if suc else "预约失败"}
                api_response_time = None
            
            status = 'success' if suc else 'failed'
            task_logger.info(f"{comment} {'预约成功' if suc else '预约失败'} - 时间: {time_slot}, 座位: {seatid}")
            if not suc and isinstance(response, dict):
                task_logger.info(f"学习通响应: {response}")

        except ValueError as e:
            status = 'error'
            error_message = f"参数错误: {str(e)}"
            suc = False
            task_logger.error(f"{comment} 参数验证失败: {e}")

        except Exception as e:
            status = 'error'
            error_message = str(e)
            suc = False

            # 根据异常类型提供更详细的错误信息
            if "login" in str(e).lower():
                task_logger.error(f"{comment} 登录相关错误: {e}")
            elif "connection" in str(e).lower() or "timeout" in str(e).lower():
                task_logger.error(f"{comment} 网络连接错误: {e}")
            elif "token" in str(e).lower():
                task_logger.error(f"{comment} Token获取错误: {e}")
            else:
                task_logger.error(f"{comment} 预约时发生未知错误: {e}", exc_info=True)
            
        finally:
            execution_time = (datetime.now() - start_time).total_seconds()
            reserve_date = (datetime.now().date() + timedelta(days=1 if reservation.get('reservation_type') == 'tomorrow' else 0)).strftime('%Y-%m-%d')

            with self.results_lock:
                self.reserve_results.append({
                    'reservation_id': reservation.get('id', -1),
                    'username': reservation.get('username', 'unknown'),
                    'roomid': reservation.get('roomid', 'unknown'),
                    'seatid': seatid,
                    'reserve_date': reserve_date,
                    'start_time': time_slot[0],
                    'end_time': time_slot[1],
                    'status': status,
                    'error_message': error_message,
                    'api_response': api_response,
                    'api_response_time': api_response_time,
                    'attempt_count': attempt_count,
                    'execution_time': execution_time
                })

            for handler in task_logger.handlers[:]:
                handler.close()
                task_logger.removeHandler(handler)
        
        return suc

    def main_reserve(self, action: bool = False):
        """主预约函数（优化数据库连接管理）"""
        try:
            # 1. 数据加载
            if not self.initialize_database():
                self.logger.error("数据库初始化失败，程序退出")
                return 1

            reservations = self.load_reservations_from_database()

            if not reservations:
                self.logger.warning("没有有效的预约任务，程序退出")
                return 0

            # 2. 预约执行（纯内存操作）
            self.logger.info(f"开始执行 {len(reservations)} 个预约任务（纯内存）...")
            exit_code = self._execute_reservations(reservations, action)
            self.logger.info("所有预约任务执行完毕")

            # 3. 结果批量写入与容错
            if self.reserve_results:
                self._save_results_with_retry(self.reserve_results)

            return exit_code

        except Exception as e:
            self.logger.error(f"主预约函数执行异常: {e}", exc_info=True)
            return 1
        finally:
            # 确保在程序结束时清理数据库连接
            self.cleanup_database()

    def _save_results_with_retry(self, results: List[Dict[str, Any]], max_retries: int = 3):
        """带重试和本地备份的批量保存机制"""
        if not results:
            self.logger.warning("没有结果需要保存")
            return

        self.logger.info(f"准备保存 {len(results)} 条预约结果")

        for attempt in range(max_retries):
            self.logger.info(f"正在尝试批量写入结果 (第 {attempt + 1}/{max_retries} 次)...")
            try:
                # 使用现有连接或重新初始化
                if not self.db_manager or not self.db_manager.is_connected():
                    if not self.initialize_database():
                        self.logger.error("数据库连接失败，无法写入结果")
                        continue

                # 执行批量保存
                self.repository.batch_save_results(results)
                self.logger.info("结果批量写入数据库成功！")
                return  # 成功写入后直接返回

            except Exception as e:
                self.logger.error(f"写入数据库时发生错误 (第 {attempt + 1} 次尝试): {e}", exc_info=True)

                # 如果是连接问题，尝试重新连接
                if "connection" in str(e).lower() or "mysql" in str(e).lower():
                    self.logger.info("检测到连接问题，尝试重新建立数据库连接...")
                    self.cleanup_database()
                    time.sleep(2)  # 等待2秒后重试连接

            if attempt < max_retries - 1:
                wait_time = (attempt + 1) * 2  # 递增等待时间
                self.logger.info(f"将在 {wait_time} 秒后重试...")
                time.sleep(wait_time)

        # 如果所有重试都失败，则执行本地备份
        self.logger.error("数据库写入多次失败，启动本地备份机制...")
        self._backup_results_to_local_file(results)

    def _backup_results_to_local_file(self, results: List[Dict[str, Any]]):
        """将结果备份到本地JSON文件"""
        try:
            backup_dir = "logs/backup"
            os.makedirs(backup_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(backup_dir, f"reservation_results_{timestamp}.json")
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=4)
            
            self.logger.info(f"预约结果已成功备份到本地文件: {backup_file}")
            self.logger.warning("请注意：这些结果需要手动恢复到数据库中。")

        except Exception as e:
            self.logger.critical(f"本地备份失败！可能导致数据丢失！错误: {e}", exc_info=True)
    
    def _execute_reservations(self, reservations: List[Dict[str, Any]], action: bool) -> int:
        """使用线程池执行所有已加载的预约任务"""
        self.logger.info("开始执行预约任务...")
        
        tasks = []
        for res in reservations:
            for time_slot in res.get('time_slots', []):
                for seatid in res.get('seatid', []):
                    tasks.append({
                        'reservation': res,
                        'time_slot': time_slot,
                        'seatid': seatid,
                        'target_time': res.get('open_reserve_time', '07:00:00'),
                        '_comment': res.get('_comment', '未知任务')
                    })

        if not tasks:
            self.logger.info("没有需要执行的预约任务")
            return 0

        self.logger.info(f"准备使用线程池 (max_workers={self.max_workers}) 执行 {len(tasks)} 个具体的预约操作")
        
        success_count = 0
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交任务到线程池
            future_to_task = {
                executor.submit(
                    self._reserve_seat_task,
                    task['reservation'], task['time_slot'], task['seatid'], action, task['target_time']
                ): task for task in tasks
            }

            # 处理已完成的任务
            for future in as_completed(future_to_task):
                task_info = future_to_task[future]
                try:
                    is_success = future.result()
                    if is_success:
                        success_count += 1
                except Exception as exc:
                    self.logger.error(f"任务 {task_info['_comment']} 执行时产生严重异常: {exc}", exc_info=True)

        self.logger.info(f"所有预约任务完成: {success_count}/{len(tasks)} 成功")

        if self.reserve_results:
            self.logger.info(f"共记录 {len(self.reserve_results)} 条结果")

        return 0 if success_count > 0 else 1


def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=getattr(logging, config.get('logging', 'level', 'INFO')),
        format=config.get('logging', 'format', '%(asctime)s - %(levelname)s - %(message)s')
    )
    
    parser = argparse.ArgumentParser(prog='Chao Xing seat auto reserve (Database Version)')
    parser.add_argument('-m','--method', default="reserve", choices=["reserve", "debug", "stats"], help='运行模式')
    parser.add_argument('-a','--action', action="store_true", help='GitHub Action模式')
    parser.add_argument('-w','--worker', help='指定工作节点ID')
    parser.add_argument('-s','--schools', help='指定要处理的学校名称列表，用逗号分隔，如：安徽师范大学,合肥工业大学')
    args = parser.parse_args()
    
    # 如果指定了工作节点ID，更新配置
    if args.worker:
        config._config['server']['worker_id'] = args.worker

    manager = DatabaseReservationManager()

    # 如果指定了学校筛选，设置学校筛选条件
    if args.schools:
        school_names = [name.strip() for name in args.schools.split(',') if name.strip()]
        manager.set_school_filter(school_names)
    
    if args.method == "reserve":
        exit_code = manager.main_reserve(args.action)
        sys.exit(exit_code)
    elif args.method == "stats":
        # 显示统计信息
        if manager.initialize_database():
            stats = manager.repository.get_reservation_stats(manager.worker_id)
            print(f"预约统计信息 (worker_id: {manager.worker_id}):")
            print(f"总尝试次数: {stats.get('total_attempts', 0)}")
            print(f"成功次数: {stats.get('success_count', 0)}")
            print(f"失败次数: {stats.get('failed_count', 0)}")
            print(f"错误次数: {stats.get('error_count', 0)}")
            print(f"成功率: {stats.get('success_rate', 0):.2f}%")
            manager.cleanup_database()
    else:
        print("调试模式暂未实现")


if __name__ == "__main__":
    main()
