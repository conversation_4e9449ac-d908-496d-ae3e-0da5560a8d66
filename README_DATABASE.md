# MySQL数据库集成版本使用说明

## 概述

本版本将xuexitong_pro座位预约系统从JSON配置文件迁移到MySQL数据库，支持多服务器部署和宝塔cron定时任务。

## 功能特性

- ✅ MySQL数据库存储预约配置
- ✅ 多服务器部署支持（通过worker_id区分）
- ✅ 智能时间段拆分（超过最大预约小时数自动拆分）
- ✅ 预约日志记录和统计
- ✅ 灵活的配置管理（环境变量 + 配置文件）
- ✅ 异常处理和重连机制
- ✅ 简洁的日志输出

## 安装步骤

### 1. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 2. 创建数据库

在MySQL中执行以下命令：

```sql
-- 创建数据库
CREATE DATABASE seat_reservation CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入表结构
SOURCE database/schema.sql;
```

### 3. 配置数据库连接

#### 方法1：环境变量（推荐）

```bash
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=root
export DB_DATABASE=seat_reservation
export WORKER_ID=server1
```

#### 方法2：配置文件

创建 `config/database.json`：

```json
{
    "database": {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "root",
        "database": "seat_reservation"
    },
    "server": {
        "worker_id": "server1"
    }
}
```

### 4. 添加预约配置

在数据库中插入预约配置：

```sql
INSERT INTO reservations (
    workerid, username, password, roomid, seatid, 
    starttime, endtime, daysofweek, comment, 
    reservation_type, max_reservation_hours
) VALUES 
(
    'server1', 
    '你的学习通账号', 
    '你的学习通密码', 
    '房间ID', 
    '["座位号1", "座位号2"]',
    '08:00:00', 
    '18:00:00',
    '["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]',
    '用户备注',
    'tomorrow',
    4
);
```

## 使用方法

### 基本使用

```bash
# 运行预约程序
python main_db.py

# 指定工作节点ID
python main_db.py --worker server2

# GitHub Action模式
python main_db.py --action

# 查看统计信息
python main_db.py --method stats
```

### 测试功能

```bash
# 运行功能测试
python test_database.py
```

### 宝塔cron定时任务

在宝塔面板中添加定时任务：

```bash
# 每天早上6:59执行预约
59 6 * * * cd /path/to/xuexitong_pro && python main_db.py --worker server1
```

## 多服务器部署

1. 在每台服务器上部署相同的代码
2. 为每台服务器设置不同的 `worker_id`
3. 在数据库中为不同的 `worker_id` 配置不同的预约任务
4. 每台服务器只会处理属于自己 `worker_id` 的任务

示例：
- 服务器1：`WORKER_ID=server1`
- 服务器2：`WORKER_ID=server2`
- 服务器3：`WORKER_ID=server3`

## 配置说明

### 数据库表字段说明

- `workerid`: 服务器工作ID，用于多服务器部署
- `username/password`: 学习通账号密码
- `roomid`: 房间ID
- `seatid`: 座位号列表（JSON格式）
- `starttime/endtime`: 预约时间段
- `daysofweek`: 预约星期（JSON格式）
- `open_reserve_time`: 开放预约时间
- `reservation_type`: 预约类型（today/tomorrow）
- `max_reservation_hours`: 最大预约小时数
- `enable_slider`: 是否启用滑块验证
- `max_attempt`: 最大尝试次数
- `sleep_time`: 请求间隔时间

### 环境变量配置

| 环境变量 | 说明 | 默认值 |
|---------|------|--------|
| DB_HOST | 数据库主机 | localhost |
| DB_PORT | 数据库端口 | 3306 |
| DB_USER | 数据库用户名 | root |
| DB_PASSWORD | 数据库密码 | root |
| DB_DATABASE | 数据库名 | seat_reservation |
| WORKER_ID | 工作节点ID | server1 |
| SLEEP_TIME | 请求间隔时间 | 0.2 |
| MAX_ATTEMPT | 最大尝试次数 | 3 |
| ENABLE_SLIDER | 启用滑块验证 | false |
| RESERVE_NEXT_DAY | 预约明天 | true |

## 日志和监控

- 预约执行日志存储在 `reservation_logs` 表中
- 可通过 `--method stats` 查看统计信息
- 支持文件日志记录（logs目录）

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接参数是否正确
   - 确认防火墙设置

2. **没有预约任务**
   - 检查 `worker_id` 是否正确
   - 确认数据库中有对应的活跃配置
   - 验证今天是否在预约星期范围内

3. **预约失败**
   - 检查学习通账号密码是否正确
   - 确认房间ID和座位号是否有效
   - 查看详细错误日志

### 调试命令

```bash
# 测试数据库连接
python test_database.py

# 查看配置信息
python -c "from config import config; print(config)"

# 查看预约配置
python -c "
from database import DatabaseManager, ReservationRepository
from config import config
db = DatabaseManager(**config.get_database_config())
db.connect()
repo = ReservationRepository(db)
print(repo.get_active_reservations_by_worker('server1'))
"
```

## 注意事项

1. 确保数据库字符集为 `utf8mb4`
2. 定期备份数据库数据
3. 监控预约成功率和错误日志
4. 合理设置最大预约小时数，避免违反平台规则
5. 在生产环境中使用强密码和安全的数据库连接
