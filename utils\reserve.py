from utils import AES_Encrypt, enc, generate_captcha_key
import json
import requests
import re
import time
import logging
import datetime
import random
from urllib3.exceptions import InsecureRequestWarning

def wait_until_specific_time(target_time, reserve_next_day=False):
    target_hour, target_minute, target_second = map(int, target_time.split(':'))
    now = datetime.datetime.now()
    current_seconds = now.hour * 3600 + now.minute * 60 + now.second
    target_seconds = target_hour * 3600 + target_minute * 60 + target_second
    
    if target_seconds < current_seconds and reserve_next_day:
        target_seconds += 24 * 3600
    elif target_seconds < current_seconds:
        logging.info(f"目标时间 {target_time} 已过，立即执行")
        return
    
    seconds_to_wait = target_seconds - current_seconds
    
    if seconds_to_wait > 10:
        minutes_to_wait = seconds_to_wait // 60
        seconds_remainder = seconds_to_wait % 60
        logging.info(f"需要等待 {minutes_to_wait} 分 {seconds_remainder} 秒")
        time.sleep(seconds_to_wait - 5)
        while datetime.datetime.now().hour != target_hour or datetime.datetime.now().minute != target_minute or datetime.datetime.now().second != target_second:
            time.sleep(0.1)
    else:
        time.sleep(0.2)
    return

def get_date(day_offset: int=0):
    today = datetime.datetime.now().date()
    offset_day = today + datetime.timedelta(days=day_offset)
    tomorrow = offset_day.strftime("%Y-%m-%d")
    return tomorrow

class reserve:
    def __init__(self, sleep_time=0.2, max_attempt=50, enable_slider=False, reserve_next_day=False, reservation_type=None, logger=None):
        # 设置日志记录器，如果没有传入则使用默认的logging模块
        self.logger = logger if logger else logging.getLogger(__name__)

        self.login_page = "https://passport2.chaoxing.com/mlogin?loginType=1&newversion=true&fid="
        self.url = "https://office.chaoxing.com/front/third/apps/seat/code?id={}&seatNum={}"
        self.submit_url = "https://office.chaoxing.com/data/apps/seat/submit"
        self.seat_url = "https://office.chaoxing.com/data/apps/seat/getusedtimes"
        self.login_url = "https://passport2.chaoxing.com/fanyalogin"
        self.token = ""
        self.success_times = 0
        self.fail_dict = []
        self.submit_msg = []
        self.requests = requests.session()
        self.token_pattern = re.compile("token = '(.*?)'")
        self.headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha.chaoxing.com",
            "Pragma": 'no-cache',
            "Sec-Ch-Ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
            'Sec-Ch-Ua-Mobile':'?0',
            'Sec-Ch-Ua-Platform':'"Linux"',
            'Sec-Fetch-Dest':'document',
            'Sec-Fetch-Mode':'navigate',
            'Sec-Fetch-Site':'none',
            'Sec-Fetch-User':'?1',
            'Upgrade-Insecure-Requests':'1',
            'User-Agent':'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        self.login_headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638",
            "X-Requested-With": "XMLHttpRequest",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Host": "passport2.chaoxing.com"
        }

        self.sleep_time = sleep_time
        self.max_attempt = max_attempt
        self.enable_slider = enable_slider

        # 统一预约类型处理：优先使用 reservation_type，向后兼容 reserve_next_day
        self.reservation_type = self._normalize_reservation_type(reservation_type, reserve_next_day)

        # 为了向后兼容，保留 reserve_next_day 属性
        self.reserve_next_day = self._should_reserve_next_day(self.reservation_type)

        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

    def _normalize_reservation_type(self, reservation_type, reserve_next_day):
        """
        统一预约类型处理：优先使用 reservation_type，向后兼容 reserve_next_day

        Args:
            reservation_type: 预约类型 ('ADVANCE_ONE_DAY', 'SAME_DAY', 'tomorrow', 'today', None)
            reserve_next_day: 向后兼容的布尔值参数

        Returns:
            str: 标准化的预约类型 ('ADVANCE_ONE_DAY' 或 'SAME_DAY')
        """
        if reservation_type is not None:
            # 优先使用 reservation_type 参数
            return self._validate_and_normalize_reservation_type(reservation_type)
        else:
            # 向后兼容：从 reserve_next_day 转换
            return 'ADVANCE_ONE_DAY' if reserve_next_day else 'SAME_DAY'

    def _validate_and_normalize_reservation_type(self, reservation_type):
        """
        验证并标准化预约类型

        Args:
            reservation_type: 输入的预约类型

        Returns:
            str: 标准化的预约类型 ('ADVANCE_ONE_DAY' 或 'SAME_DAY')
        """
        # 预约类型映射表
        type_mapping = {
            # 标准业务层格式
            'ADVANCE_ONE_DAY': 'ADVANCE_ONE_DAY',
            'SAME_DAY': 'SAME_DAY',
            # 数据库格式
            'tomorrow': 'ADVANCE_ONE_DAY',
            'today': 'SAME_DAY',
            # 小写格式
            'advance_one_day': 'ADVANCE_ONE_DAY',
            'same_day': 'SAME_DAY'
        }

        normalized_type = type_mapping.get(reservation_type, 'SAME_DAY')
        if reservation_type not in type_mapping:
            self.logger.warning(f"未知的预约类型 '{reservation_type}'，使用默认值 'SAME_DAY'")

        return normalized_type

    def _should_reserve_next_day(self, reservation_type):
        """
        根据预约类型判断是否应该预约明天

        Args:
            reservation_type: 标准化的预约类型

        Returns:
            bool: True表示预约明天，False表示预约今天
        """
        return reservation_type == 'ADVANCE_ONE_DAY'

    # login and page token
    def _get_page_token(self, url):
        """
        从页面提取 token 和 submit_enc 值

        Args:
            url: 页面URL

        Returns:
            tuple: (token, submit_enc) 元组
        """
        try:
            response = self.requests.get(url=url, verify=False)
            html = response.content.decode('utf-8')

            # 提取 token 值 (格式: token = 'xxx')
            token_match = re.search(r'token = \'([^\']+)\'', html)
            token = token_match.group(1) if token_match else ""

            # 如果主要模式失败，尝试其他token匹配模式
            if not token:
                token_patterns = [
                    r'token\s*=\s*[\'\"](.*?)[\'\"]',  # token = '...' 格式（最常见）
                    r'token:\s*[\'\"](.*?)[\'\"]',     # token: '...' 格式（原始模式）
                    r'token\s*:\s*[\'\"](.*?)[\'\"]',  # 带空格的冒号模式
                    r'_token[\'\"]\s*:\s*[\'\"](.*?)[\'\"]',  # _token模式
                    r'csrfToken[\'\"]\s*:\s*[\'\"](.*?)[\'\"]',  # csrf token模式
                    r'var\s+token\s*=\s*[\'\"](.*?)[\'\"]',  # JavaScript var token = '...'
                    r'let\s+token\s*=\s*[\'\"](.*?)[\'\"]',  # JavaScript let token = '...'
                    r'const\s+token\s*=\s*[\'\"](.*?)[\'\"]',  # JavaScript const token = '...'
                    r'name=[\'\"]\s*_token[\'\"]\s*value=[\'\"](.*?)[\'\"]',  # input hidden模式
                    r'name=[\'\"]\s*token[\'\"]\s*value=[\'\"](.*?)[\'\"]',  # token input模式
                    r'<input[^>]*name=[\'\"]\s*_token[\'\"]\s*[^>]*value=[\'\"](.*?)[\'\"]',  # 完整input标签
                    r'<input[^>]*name=[\'\"]\s*token[\'\"]\s*[^>]*value=[\'\"](.*?)[\'\"]',  # 完整token input标签
                ]

                # 记录每个模式的匹配尝试
                for i, pattern in enumerate(token_patterns):
                    matches = re.findall(pattern, html, re.IGNORECASE)
                    if matches:
                        token = matches[0]
                        break

            # 提取 submit_enc 值 (格式: <input id="submit_enc" value="xxx"/>)
            submit_enc_match = re.search(r'id="submit_enc"[^>]*value="([^"]*)"', html)
            submit_enc = submit_enc_match.group(1) if submit_enc_match else ""

            if not token:
                self.logger.warning(f"未找到token！URL: {url}")
            if not submit_enc:
                self.logger.warning(f"未找到submit_enc值！URL: {url}")

            return token, submit_enc

        except Exception as e:
            self.logger.error(f"获取页面 token 和 submit_enc 失败: {e}")
            return "", ""

    def get_login_status(self):
        self.requests.headers = self.login_headers
        self.requests.get(url=self.login_page, verify=False)

    def login(self, username, password):
        username = AES_Encrypt(username)
        password = AES_Encrypt(password)
        parm = {
            "fid": -1,
            "uname": username,
            "password": password,
            "refer": "http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode%3Fid%3D4219%26seatNum%3D380",
            "t": True
        }
        jsons = self.requests.post(
            url=self.login_url, params=parm, verify=False)
        obj = jsons.json()
        if obj['status']:
            self.logger.info(f"User {username} login successfully")
            return (True, '')
        else:
            self.logger.info(f"User {username} login failed. Please check you password and username! ")
            return (False, obj['msg2'])

    # extra: get roomid
    def roomid(self, encode):
        url = f"https://office.chaoxing.com/data/apps/seat/room/list?cpage=1&pageSize=100&firstLevelName=&secondLevelName=&thirdLevelName=&deptIdEnc={encode}"
        json_data = self.requests.get(url=url).content.decode('utf-8')
        ori_data = json.loads(json_data)
        for i in ori_data["data"]["seatRoomList"]:
            info = f'{i["firstLevelName"]}-{i["secondLevelName"]}-{i["thirdLevelName"]} id为：{i["id"]}'
            print(info)

    # solve captcha 

    def resolve_captcha(self):
        self.logger.info(f"Start to resolve captcha token")
        captcha_token, bg, tp = self.get_slide_captcha_data()
        self.logger.info(f"Successfully get prepared captcha_token {captcha_token}")
        self.logger.info(f"Captcha Image URL-small {tp}, URL-big {bg}")
        x = self.x_distance(bg, tp)
        self.logger.info(f"Successfully calculate the captcha distance {x}")

        params = {
            "callback": "jQuery33109180509737430778_1716381333117",
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",
            "type": "slide",
            "token": captcha_token,
            "textClickArr": json.dumps([{"x": x}]),
            "coordinate": json.dumps([]),
            "runEnv": "10",
            "version": "1.1.18",
            "_": int(time.time() * 1000)
        }
        response = self.requests.get(
            f'https://captcha.chaoxing.com/captcha/check/verification/result', params=params, headers=self.headers)
        text = response.text.replace('jQuery33109180509737430778_1716381333117(', "").replace(')', "")
        data = json.loads(text)
        self.logger.info(f"Successfully resolve the captcha token {data}")
        try:
           validate_val = json.loads(data["extraData"])['validate']
           return validate_val
        except KeyError as e:
            self.logger.info("Can't load validate value. Maybe server return mistake.")
            return ""

    def get_slide_captcha_data(self):
        url = "https://captcha.chaoxing.com/captcha/get/verification/image"
        timestamp = int(time.time() * 1000)
        capture_key, token = generate_captcha_key(timestamp)
        referer = f"https://office.chaoxing.com/front/third/apps/seat/code?id=3993&seatNum=0199"
        params = {
            "callback": f"jQuery33107685004390294206_1716461324846",
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",
            "type": "slide",
            "version": "1.1.18",
            "captchaKey": capture_key,
            "token": token,
            "referer": referer,
            "_": timestamp,
            "d": "a",
            "b": "a"
        }
        response = self.requests.get(url=url, params=params, headers=self.headers)
        content = response.text
        
        data = content.replace("jQuery33107685004390294206_1716461324846(",
                            ")").replace(")", "")
        data = json.loads(data)
        captcha_token = data["token"]
        bg = data["imageVerificationVo"]["shadeImage"]
        tp = data["imageVerificationVo"]["cutoutImage"]
        return captcha_token, bg, tp
    
    def x_distance(self, bg, tp):
        import numpy as np
        import cv2
        def cut_slide(slide):
            slider_array = np.frombuffer(slide, np.uint8)
            slider_image = cv2.imdecode(slider_array, cv2.IMREAD_UNCHANGED)
            slider_part = slider_image[:, :, :3]
            mask = slider_image[:, :, 3]
            mask[mask != 0] = 255
            x, y, w, h = cv2.boundingRect(mask)
            cropped_image = slider_part[y:y + h, x:x + w]
            return cropped_image
        c_captcha_headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha-b.chaoxing.com",
            "Pragma" : 'no-cache',
            "Sec-Ch-Ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
            'Sec-Ch-Ua-Mobile':'?0',
            'Sec-Ch-Ua-Platform':'"Linux"',
            'Sec-Fetch-Dest':'document',
            'Sec-Fetch-Mode':'navigate',
            'Sec-Fetch-Site':'none',
            'Sec-Fetch-User':'?1',
            'Upgrade-Insecure-Requests':'1',
            'User-Agent':'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        bgc, tpc = self.requests.get(bg, headers=c_captcha_headers), self.requests.get(tp, headers=c_captcha_headers)
        bg, tp = bgc.content, tpc.content 
        bg_img = cv2.imdecode(np.frombuffer(bg, np.uint8), cv2.IMREAD_COLOR)  
        tp_img = cut_slide(tp)
        bg_edge = cv2.Canny(bg_img, 100, 200)
        tp_edge = cv2.Canny(tp_img, 100, 200)
        bg_pic = cv2.cvtColor(bg_edge, cv2.COLOR_GRAY2RGB)
        tp_pic = cv2.cvtColor(tp_edge, cv2.COLOR_GRAY2RGB)
        res = cv2.matchTemplate(bg_pic, tp_pic, cv2.TM_CCOEFF_NORMED)
        _, _, _, max_loc = cv2.minMaxLoc(res)  
        tl = max_loc
        return tl[0]

    def submit(self, times, roomid, seatid, action, submit_time=None, wait_time=0.5, school_name="Unknown", school_seldom=0):
        for seat in seatid:
            # 构建完整URL
            full_url = self.url.format(roomid, seat)

            suc = False
            first_response_time = None  # 跟踪首次API响应时间
            response = None  # 保存最终响应
            is_first_attempt = True  # 跟踪是否为首次尝试

            while not suc and self.max_attempt > 0:
                token, submit_enc = self._get_page_token(full_url)
                self.logger.info(f"Get token: {token}")
                self.logger.info(f"Get submit_enc: {submit_enc}")

                # 如果token为空，重试一次
                if not token:
                    self.logger.warning("Token为空，尝试重新获取...")
                    time.sleep(1)  # 等待1秒后重试
                    token, submit_enc = self._get_page_token(full_url)

                captcha = self.resolve_captcha() if self.enable_slider else ""
                response = self.get_submit(
                    self.submit_url, times=times, token=token, roomid=roomid,
                    seatid=seat, captcha=captcha, action=action,
                    submit_time=submit_time, wait_time=wait_time, school_name=school_name,
                    school_seldom=school_seldom,
                    first_response_time=first_response_time,  # 传递首次响应时间
                    is_first_attempt=is_first_attempt,  # 传递首次尝试标志
                    submit_enc_value=submit_enc  # 传递 submit_enc 值
                )

                # 记录首次响应时间（只记录一次）
                if first_response_time is None and isinstance(response, dict) and '_response_time' in response:
                    first_response_time = response['_response_time']

                # 处理新的响应格式
                if isinstance(response, dict):
                    if response.get("success", False):
                        # 确保返回的响应包含首次响应时间
                        if first_response_time:
                            response['_first_response_time'] = first_response_time
                        return response
                    # 如果失败，继续重试
                elif response:  # 兼容旧的布尔返回值
                    return {"success": True, "msg": "预约成功", "_first_response_time": first_response_time}

                time.sleep(self.sleep_time)
                self.max_attempt -= 1
                is_first_attempt = False  # 后续尝试不再是首次尝试

        # 如果所有尝试都失败，返回最后一次的响应或默认失败响应
        if isinstance(response, dict):
            # 确保返回的响应包含首次响应时间
            if first_response_time:
                response['_first_response_time'] = first_response_time
            return response
        return {"success": False, "msg": "预约失败，已达到最大重试次数", "_first_response_time": first_response_time}

    def sign(self):
        try:
            # 先获取预约列表
            response = self.requests.get(
                url='https://office.chaoxing.com/data/apps/seat/reservelist?'
                'indexId=0&'
                'pageSize=100&'
                'type=-1'
            ).json()
            
            if 'data' not in response:
                self.logger.info(f"Failed to get reservation list: {response}")
                return []
            
            reserveList = response['data']['reserveList']
            result = []
            
            # 遍历预约列表并尝试签到
            for reservation in reserveList:
                if reservation['type'] == -1:
                    if reservation['today'] == get_date(0) or reservation['today'] == get_date(1):
                        # 获取预约ID并尝试签到
                        reservation_id = reservation['id']
                        sign_response = self.requests.get(
                            url=f'https://office.chaoxing.com/data/apps/seat/sign?id={reservation_id}'
                        ).json()
                        
                        reservation['sign_status'] = '签到成功' if sign_response.get('success') else sign_response.get('msg', '签到失败')
                        result.append(reservation)
                        self.logger.info(f"Seat {reservation['seatNum']} sign status: {reservation['sign_status']}")

            return result
        except Exception as e:
            self.logger.error(f"Error during sign process: {e}")
            return []
    
    # 获取到最近一次预约的座位ID
    def get_my_seat_id(self):
        # seatId 不一定为602 仅为演示
        # 注意：此方法当前未被使用，保留以备将来需要
        # 需要定义 self.version, self.session (应为 self.requests), self.today, self.tomorrow
        try:
            response = self.requests.get(url='https://office.chaoxing.com/data/apps/seat/reservelist?'
                                            'indexId=0&'
                                            'pageSize=100&'
                                            'type=-1').json()['data']['reserveList']
            result = []
            today = get_date(0)
            tomorrow = get_date(1)
            for index in response:
                if index['type'] == -1:
                    if index['today'] == today or index['today'] == tomorrow:
                        result.append(index)
            return result
        except Exception as e:
            self.logger.error(f"Error getting seat id: {e}")
            return [] 

    def get_submit(self, url, times, token, roomid, seatid, captcha="", action=False, submit_time=None, wait_time=0.5, school_name="Unknown", school_seldom=0, first_response_time=None, is_first_attempt=True, submit_enc_value=None):
        # ============================================================================
        # 阶段1: 预处理阶段 - 准备所有可以提前计算的数据
        # ============================================================================

        # 预先导入所需模块（避免在关键路径上导入）
        import json
        import urllib.parse

        # 记录预约提交开始时间戳
        submit_timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]

        # 统一的预约类型和日期计算逻辑
        base_day = datetime.datetime.now().date()

        # 根据标准化的预约类型计算日期偏移
        if self.reservation_type == 'ADVANCE_ONE_DAY':
            delta_day = 1  # 预约明天
        else:  # 'SAME_DAY'
            delta_day = 0  # 预约今天

        day = base_day + datetime.timedelta(days=delta_day)

        # 预约类型分析（用于日志记录）
        reservation_type_info = {
            "reservation_type": self.reservation_type,
            "reserve_next_day": self.reserve_next_day,  # 向后兼容
            "action": action,
            "delta_day": delta_day,
            "base_date": str(base_day),
            "calculated_date": str(day),
            "school_name": school_name,
            "wait_time": wait_time,
            "school_seldom": school_seldom
        }

        if action:
            day = base_day + datetime.timedelta(days=1+delta_day)  # 由于action时区问题导致其早+8区一天
            reservation_type_info["action_adjusted_date"] = str(day)
            reservation_type_info["action_note"] = "由于action时区问题导致其早+8区一天"

        # 构建预约参数
        parm = {
            "roomId": roomid,
            "startTime": times[0],
            "endTime": times[1],
            "day": str(day),
            "seatNum": seatid,
            "captcha": captcha,
            "token": token
        }

        # 生成加密签名（必须在等待前完成）
        parm["enc"] = enc(parm, submit_enc_value)

        # 预先构建请求头（避免在关键时刻构建）
        prepared_headers = {
            **self.login_headers,
            'Connection': 'keep-alive',
            'Keep-Alive': 'timeout=10'
        }

        # 预先读取随机等待配置（如果需要）
        random_wait_config = None
        if is_first_attempt and school_seldom == 1:
            try:
                from config.settings import config
                random_wait_config = config.get_random_wait_range()
            except Exception as e:
                self.logger.warning(f"读取随机等待配置失败: {e}")
                random_wait_config = {'min': 0, 'max': 3}  # 默认值

        # 预先构建URL和进行参数验证（在等待前完成）
        query_string = urllib.parse.urlencode(parm)
        full_url = f"{url}?{query_string}"

        # 参数完整性检查（在等待前完成）
        required_params = ['roomId', 'startTime', 'endTime', 'day', 'seatNum', 'token', 'enc']
        missing_params = [param for param in required_params if not parm.get(param)]

        # 详细日志记录（在等待前完成，避免影响关键时刻）
        self.logger.info(f"{'='*60}")
        self.logger.info(f"预约提交详细信息 - 时间戳: {submit_timestamp}")
        self.logger.info(f"学校: {school_name}")
        self.logger.info(f"{'='*60}")

        # 预约类型和日期计算分析
        self.logger.info(f"=== 预约类型分析 ===")
        self.logger.info(f"预约类型详情: {json.dumps(reservation_type_info, ensure_ascii=False, indent=2)}")

        # 原始参数记录
        self.logger.info(f"=== 预约参数详情 ===")
        self.logger.info(f"完整参数（包含签名）: {json.dumps(parm, ensure_ascii=False, indent=2)}")

        # URL构建信息
        self.logger.info(f"=== 提交URL构建 ===")
        self.logger.info(f"基础URL: {url}")
        self.logger.info(f"完整提交URL: {full_url}")
        self.logger.info(f"URL总长度: {len(full_url)} 字符")

        # 参数完整性检查结果
        self.logger.info(f"=== 参数完整性检查 ===")
        if missing_params:
            self.logger.warning(f"缺失必要参数: {missing_params}")
        else:
            self.logger.info(f"所有必要参数完整: ✓")

        # 验证码状态
        if captcha:
            self.logger.info(f"验证码状态: 已提供 (长度: {len(captcha)})")
        else:
            self.logger.info(f"验证码状态: 未提供 (可能未启用滑块验证)")

        # ============================================================================
        # 阶段2: 智能等待阶段 - 在等待期间进行必要的准备
        # ============================================================================

        # 定时提交等待（如果指定了提交时间）
        if submit_time:
            self.logger.info(f"=== 定时提交等待 ===")
            self.logger.info(f"等待直到指定时间 {submit_time} 再进行提交...")
            # 注意：定时等待与预约日期无关，总是等待今天的指定时间
            # reserve_next_day 只影响预约日期计算，不影响定时等待逻辑
            self._wait_until_specific_time(submit_time, reserve_next_day=False)
            self.logger.info(f"到达指定时间 {submit_time}，准备进入学校等待策略...")

        # 学校特定等待策略（只在首次尝试时执行）
        if is_first_attempt:
            self.logger.info(f"=== 学校特定等待策略 ===")

            # 学校特定等待时间
            if wait_time > 0:
                self.logger.info(f"根据学校 [{school_name}] 设置，额外等待 {wait_time} 秒...")
                time.sleep(wait_time)
                self.logger.info(f"学校等待结束，继续执行...")

            # 随机等待时间（使用预先读取的配置）
            if school_seldom == 1 and random_wait_config:
                min_wait = random_wait_config.get('min', 0)
                max_wait = random_wait_config.get('max', 3)

                # 生成随机等待时间
                random_wait_time = random.uniform(min_wait, max_wait)
                self.logger.info(f"学校 [{school_name}] 启用了随机等待，将额外等待 {random_wait_time:.2f} 秒...")
                time.sleep(random_wait_time)
                self.logger.info(f"随机等待结束，准备发送预约请求...")
        else:
            self.logger.debug(f"重试阶段，跳过等待逻辑，直接发送预约请求...")

        # ============================================================================
        # 阶段3: 快速提交阶段 - 最小化关键路径操作，快速发送请求
        # ============================================================================

        # 快速记录请求发送时间（最小化操作）
        request_start_time = datetime.datetime.now()
        self.logger.info(f"=== HTTP请求发送 ===")

        try:
            # 使用预先准备的请求头，快速发送请求
            html = self.requests.post(
                url=url,
                params=parm,
                verify=True,
                timeout=(2, 2),
                headers=prepared_headers
            ).content.decode('utf-8')

            # 快速记录响应时间
            response_time = datetime.datetime.now()
            response_duration = (response_time - request_start_time).total_seconds() * 1000

            # 延后详细的时间日志记录（避免影响后续处理）
            self.logger.info(f"请求耗时: {response_duration:.2f} 毫秒")

        except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
            self.logger.error(f"=== 请求失败 - 第一次尝试 ===")
            self.logger.error(f"连接错误或超时: {str(e)}")
            self.logger.info(f"等待2秒后进行重试...")
            time.sleep(2)  # 等待2秒后重试

            try:
                retry_start_time = datetime.datetime.now()
                self.logger.info(f"=== 重试请求发送 ===")

                # 重试时使用更长的超时时间，但仍使用预先准备的请求头
                prepared_headers_retry = prepared_headers.copy()
                prepared_headers_retry['Keep-Alive'] = 'timeout=120'

                html = self.requests.post(
                    url=url,
                    params=parm,
                    verify=True,
                    timeout=(2, 2),
                    headers=prepared_headers_retry
                ).content.decode('utf-8')

                retry_response_time = datetime.datetime.now()
                retry_duration = (retry_response_time - retry_start_time).total_seconds() * 1000
                self.logger.info(f"重试耗时: {retry_duration:.2f} 毫秒")

            except Exception as e:
                self.logger.error(f"=== 重试失败 ===")
                self.logger.error(f"重试失败原因: {str(e)}")
                self.logger.error(f"预约提交彻底失败，返回False")
                return False

        # ============================================================================
        # 阶段4: 快速响应处理 - 优化响应解析和结果处理
        # ============================================================================

        # 快速解析响应
        try:
            response_data = json.loads(html)

            # 快速提取关键信息
            success = response_data.get("success", False)
            message = response_data.get("message", "无消息")

            # 只在首次响应时设置时间戳
            if first_response_time is None:
                current_response_time = datetime.datetime.now()
                response_data['_response_time'] = current_response_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]
            else:
                response_data['_response_time'] = first_response_time

            # 快速记录关键结果
            self.logger.info(f"=== 预约结果 ===")
            if success:
                self.logger.info(f"✓ 预约成功! {message}")
            else:
                self.logger.warning(f"✗ 预约失败: {message}")

            # 记录到提交消息列表（保持兼容性）
            result_summary = f"{times[0]}~{times[1]}: {response_data}"
            self.submit_msg.append(result_summary)

            # 延后详细日志记录（避免影响性能关键路径）
            self._log_detailed_response(response_data, html, message, success)

            return response_data  # 返回完整响应

        except json.JSONDecodeError as e:
            self.logger.error(f"=== 响应解析失败 ===")
            self.logger.error(f"JSON解析错误: {str(e)}")
            self.logger.error(f"原始响应内容: {html}")
            return False

    def _log_detailed_response(self, response_data, html, message, success):
        """延后的详细响应日志记录（避免影响关键路径性能）"""
        import json

        self.logger.info(f"=== 详细响应信息 ===")
        self.logger.info(f"原始响应内容: {html}")
        self.logger.info(f"解析后响应: {json.dumps(response_data, ensure_ascii=False, indent=2)}")

        # 失败原因分析
        if not success:
            if "已预约" in message:
                self.logger.info(f"失败类型: 重复预约")
            elif "验证码" in message:
                self.logger.info(f"失败类型: 验证码问题")
            elif "座位" in message:
                self.logger.info(f"失败类型: 座位相关问题")
            elif "时间" in message:
                self.logger.info(f"失败类型: 时间相关问题")
            else:
                self.logger.info(f"失败类型: 其他原因")

        self.logger.info(f"{'='*60}")
        self.logger.info(f"预约提交完成 - 结果: {'成功' if success else '失败'}")
        self.logger.info(f"{'='*60}")

    def _wait_until_specific_time(self, target_time, reserve_next_day=False):
        """等待直到指定时间，使用任务专用日志记录器"""
        target_hour, target_minute, target_second = map(int, target_time.split(':'))
        now = datetime.datetime.now()
        current_seconds = now.hour * 3600 + now.minute * 60 + now.second
        target_seconds = target_hour * 3600 + target_minute * 60 + target_second

        # 如果目标时间已过，立即执行（无论 reserve_next_day 是什么值）
        if target_seconds < current_seconds:
            self.logger.info(f"目标时间 {target_time} 已过，立即执行")
            return

        # 如果目标时间未到，且设置了 reserve_next_day，则等到明天的同一时间
        if target_seconds >= current_seconds and reserve_next_day:
            target_seconds += 24 * 3600
            self.logger.info(f"设置了 reserve_next_day，将等到明天的 {target_time}")

        seconds_to_wait = target_seconds - current_seconds

        if seconds_to_wait > 10:
            minutes_to_wait = seconds_to_wait // 60
            seconds_remainder = seconds_to_wait % 60
            self.logger.info(f"需要等待 {minutes_to_wait} 分 {seconds_remainder} 秒")
            time.sleep(seconds_to_wait - 5)
            while datetime.datetime.now().hour != target_hour or datetime.datetime.now().minute != target_minute or datetime.datetime.now().second != target_second:
                time.sleep(0.1)
        else:
            time.sleep(0.2)
        return
