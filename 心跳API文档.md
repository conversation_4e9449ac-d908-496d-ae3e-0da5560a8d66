# SeatMaster 心跳API文档

## 概述

SeatMaster系统采用心跳机制来监控和管理分布式副服务器的状态。主服务器通过接收副服务器定期发送的心跳信息，实时掌握各副服务器的运行状态、负载情况和健康状况。

### 核心特性

- **自动注册**：未注册的副服务器可通过心跳自动注册
- **负载均衡**：基于实际任务分配情况计算服务器负载
- **故障检测**：自动检测心跳超时并标记服务器离线
- **IP追踪**：记录副服务器的真实IP地址
- **状态管理**：支持多种服务器状态管理

---

## API接口规范

### 1. 心跳接口

#### 1.1 基本信息
```
POST /api/worker/heartbeat/{id}
Content-Type: application/json
认证要求: 无需认证
```

#### 1.2 路径参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | String | 是 | 副服务器的工作节点ID（如：worker-001） |

#### 1.3 请求体参数
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| workerId | String | 否 | 工作节点ID（与路径参数一致） |
| status | String | 是 | 服务器状态：ONLINE/OFFLINE/BUSY/MAINTENANCE |
| currentLoad | Integer | 否 | 当前负载（正在处理的任务数） |
| timestamp | Long | 否 | 时间戳（毫秒） |

#### 1.4 请求示例
```http
POST /api/worker/heartbeat/worker-001
Content-Type: application/json

{
  "workerId": "worker-001",
  "status": "ONLINE",
  "currentLoad": 3,
  "timestamp": 1719564000000
}
```

#### 1.5 响应格式
```json
{
  "code": 200,
  "message": "心跳更新成功",
  "data": "心跳更新成功",
  "success": true
}
```

#### 1.6 错误响应
```json
{
  "code": 500,
  "message": "心跳更新失败: 具体错误信息",
  "data": null,
  "success": false
}
```

---

## 相关接口

### 2. 副服务器注册接口

```
POST /api/worker/register
Content-Type: application/json
```

**请求体：**
```json
{
  "workerId": "worker-001",
  "name": "SeatMaster Worker Server Simple",
  "host": "localhost",
  "port": 8082,
  "maxConcurrentTasks": 10,
  "supportedOperations": ["XUEXITONG_RESERVATION"]
}
```

### 3. 副服务器注销接口

```
POST /api/worker/unregister/{id}
```

### 4. 健康检查接口

```
GET /api/worker/health
```

**响应示例：**
```json
{
  "code": 200,
  "message": "健康检查通过",
  "data": {
    "status": "UP",
    "timestamp": 1719564000000,
    "service": "worker-heartbeat-controller"
  },
  "success": true
}
```

---

## 管理员接口

### 5. 管理员心跳接口

```
POST /api/admin/worker-management/servers/{id}/heartbeat
Content-Type: application/json
认证要求: 需要管理员权限
```

### 6. 更新超时服务器状态

```
POST /api/admin/worker-management/servers/update-timeout-status?timeoutMinutes=5
认证要求: 需要管理员权限
```

**响应：**
```json
{
  "code": 200,
  "message": "更新超时服务器状态成功",
  "data": 2,
  "success": true
}
```

---

## 服务器状态管理

### 状态定义

| 状态 | 说明 |
|------|------|
| ONLINE | 在线状态，可以接收新任务 |
| OFFLINE | 离线状态，无法接收任务 |
| BUSY | 繁忙状态，负载较高 |
| MAINTENANCE | 维护状态，暂停服务 |

### 状态转换规则

1. **自动上线**：副服务器发送心跳时自动设置为ONLINE
2. **超时离线**：10分钟无心跳自动标记为OFFLINE
3. **手动维护**：管理员可手动设置为MAINTENANCE

---

## 心跳监控机制

### 发送频率
- **副服务器**：每30秒发送一次心跳
- **主服务器**：每5分钟检查一次心跳状态

### 超时设置
- **心跳超时**：10分钟无心跳视为超时
- **健康检查**：每5分钟对所有服务器执行健康检查

### 自动处理
1. **自动注册**：未注册的副服务器发送心跳时自动注册
2. **负载计算**：基于数据库中实际分配的任务数量计算负载
3. **状态更新**：自动更新服务器状态和最后心跳时间
4. **IP地址追踪**：自动记录和更新副服务器的真实IP地址

### IP地址处理机制

系统会自动获取副服务器的真实IP地址：

1. **X-Forwarded-For头部**：优先从代理头部获取真实IP
2. **X-Real-IP头部**：从Nginx等反向代理获取
3. **Remote Address**：直接连接时使用远程地址
4. **自动注册**：IP地址用于自动注册时构建服务器URL

**IP获取优先级：**
```
X-Forwarded-For > X-Real-IP > X-Forwarded > Proxy-Client-IP > Remote Address
```

---

## 配置参数

### 副服务器配置
```properties
# 副服务器配置
seatmaster.worker.id=worker-001
seatmaster.main-server.url=http://localhost:8081
seatmaster.worker.status=ONLINE

# 心跳发送间隔（毫秒）
heartbeat.interval=30000
```

### 主服务器配置
```java
// 心跳超时时间（分钟）
int timeoutMinutes = 10;

// 健康检查间隔（cron表达式）
@Scheduled(cron = "0 */5 * * * *")
```

---

## 使用示例

### 副服务器发送心跳
```java
@Scheduled(fixedRate = 30000)
public void sendHeartbeat() {
    String heartbeatUrl = mainServerUrl + "/api/worker/heartbeat/" + workerId;
    
    Map<String, Object> heartbeatData = new HashMap<>();
    heartbeatData.put("status", "ONLINE");
    heartbeatData.put("currentLoad", getCurrentLoad());
    heartbeatData.put("timestamp", System.currentTimeMillis());
    
    restTemplate.postForObject(heartbeatUrl, heartbeatData, String.class);
}
```

### 手动发送心跳（测试）
```bash
curl -X POST http://localhost:8081/api/worker/heartbeat/worker-001 \
  -H "Content-Type: application/json" \
  -d '{
    "workerId": "worker-001",
    "status": "ONLINE",
    "currentLoad": 0,
    "timestamp": 1719564000000
  }'
```

---

## 数据库结构

### worker_servers表
```sql
CREATE TABLE worker_servers (
    id VARCHAR(50) PRIMARY KEY COMMENT '服务器ID',
    worker_id VARCHAR(50) COMMENT '工作节点ID',
    name VARCHAR(100) COMMENT '服务器名称',
    server_url VARCHAR(200) COMMENT '服务器URL',
    priority INT DEFAULT 5 COMMENT '优先级',
    supported_operations TEXT COMMENT '支持的操作(JSON)',
    status VARCHAR(20) DEFAULT 'OFFLINE' COMMENT '状态',
    current_load INT DEFAULT 0 COMMENT '当前负载',
    max_concurrent_tasks INT DEFAULT 10 COMMENT '最大并发任务数',
    total_tasks_completed BIGINT DEFAULT 0 COMMENT '总完成任务数',
    total_tasks_failed BIGINT DEFAULT 0 COMMENT '总失败任务数',
    average_execution_time DOUBLE DEFAULT 0.0 COMMENT '平均执行时间',
    last_heartbeat DATETIME COMMENT '最后心跳时间',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    description TEXT COMMENT '描述',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_worker_id (worker_id),
    INDEX idx_status (status),
    INDEX idx_last_heartbeat (last_heartbeat)
);
```

---

## 故障排查

### 常见问题

#### 1. 心跳发送失败
**现象**：副服务器日志显示"Failed to send heartbeat"
**原因**：网络连接问题或主服务器不可达
**解决**：检查网络连接和主服务器状态

#### 2. 服务器被标记为离线
**现象**：服务器状态显示OFFLINE
**原因**：心跳超时（超过10分钟未收到心跳）
**解决**：检查副服务器是否正常运行，重启副服务器

#### 3. 负载显示不准确
**现象**：显示的负载与实际不符
**原因**：系统优先使用数据库统计的任务数量
**解决**：这是正常现象，数据库统计更准确

#### 4. 自动注册失败
**现象**：副服务器发送心跳但未自动注册
**原因**：IP地址获取失败或数据格式错误
**解决**：检查网络配置和心跳数据格式

#### 5. IP地址显示错误
**现象**：服务器显示的IP地址不正确
**原因**：代理配置问题或头部信息缺失
**解决**：配置正确的代理头部（X-Forwarded-For等）

### 监控建议

1. **日志监控**：关注心跳发送和接收的日志
2. **状态监控**：定期检查服务器状态列表
3. **负载监控**：观察服务器负载分布是否均衡
4. **网络监控**：确保副服务器与主服务器网络连通
5. **IP追踪**：监控IP地址变化，确保服务器可达性

### 性能优化建议

1. **心跳频率**：根据网络状况调整心跳发送间隔
2. **超时设置**：根据业务需求调整心跳超时时间
3. **批量处理**：使用批量更新减少数据库操作
4. **缓存机制**：缓存服务器状态信息减少查询

---

## 版本信息

- **文档版本**：v1.0
- **系统版本**：SeatMaster v2.1
- **最后更新**：2025-07-22
- **维护人员**：SeatMaster开发团队
