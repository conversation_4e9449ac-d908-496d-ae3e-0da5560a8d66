"""
数据库连接管理模块
提供数据库连接池和异常处理功能
"""
import pymysql
import logging
from contextlib import contextmanager
from typing import Optional, Dict, Any
import time

class DatabaseManager:
    """数据库连接管理器"""
    
    def __init__(self, host: str = 'localhost', port: int = 3306, 
                 user: str = 'root', password: str = 'root', 
                 database: str = 'seat_reservation', charset: str = 'utf8mb4'):
        """
        初始化数据库连接管理器
        
        Args:
            host: 数据库主机
            port: 数据库端口
            user: 用户名
            password: 密码
            database: 数据库名
            charset: 字符集
        """
        self.config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'charset': charset,
            'autocommit': True,
            'connect_timeout': 10,
            'read_timeout': 10,
            'write_timeout': 10
        }
        self._connection = None
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """
        建立数据库连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            self._connection = pymysql.connect(**self.config)
            self.logger.info("数据库连接成功")
            return True
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """关闭数据库连接"""
        if self._connection:
            try:
                self._connection.close()
                self.logger.info("数据库连接已关闭")
            except Exception as e:
                self.logger.error(f"关闭数据库连接时出错: {e}")
            finally:
                self._connection = None
    
    def is_connected(self) -> bool:
        """检查连接是否有效"""
        if not self._connection:
            return False
        try:
            self._connection.ping(reconnect=True)
            return True
        except:
            return False
    
    def reconnect(self) -> bool:
        """重新连接数据库"""
        self.disconnect()
        return self.connect()
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        自动处理连接的获取和释放
        """
        if not self.is_connected():
            if not self.reconnect():
                raise Exception("无法建立数据库连接")
        
        try:
            yield self._connection
        except Exception as e:
            self.logger.error(f"数据库操作异常: {e}")
            raise
    
    def execute_query(self, sql: str, params: tuple = None) -> list:
        """
        执行查询语句
        
        Args:
            sql: SQL语句
            params: 参数
            
        Returns:
            list: 查询结果
        """
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            try:
                cursor.execute(sql, params)
                result = cursor.fetchall()
                self.logger.debug(f"查询执行成功，返回 {len(result)} 条记录")
                return result
            finally:
                cursor.close()
    
    def execute_update(self, sql: str, params: tuple = None) -> int:
        """
        执行更新语句

        Args:
            sql: SQL语句
            params: 参数

        Returns:
            int: 影响的行数
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                affected_rows = cursor.execute(sql, params)
                conn.commit()
                self.logger.debug(f"更新执行成功，影响 {affected_rows} 行")
                return affected_rows
            except Exception as e:
                conn.rollback()

    def execute_many(self, sql: str, params_list: list) -> int:
        """
        批量执行语句

        Args:
            sql: SQL语句
            params_list: 参数列表

        Returns:
            int: 影响的行数
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                cursor.executemany(sql, params_list)
                conn.commit()
                affected_rows = cursor.rowcount
                self.logger.debug(f"批量执行成功，影响 {affected_rows} 行")
                return affected_rows
            finally:
                cursor.close()
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                cursor.close()
                return result[0] == 1
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {e}")
            return False
