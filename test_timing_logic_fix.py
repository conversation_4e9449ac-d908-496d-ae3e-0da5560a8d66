#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试定时逻辑修复
验证定时等待与预约日期计算的逻辑分离
"""

import sys
import os
import datetime
import time
import logging
from unittest.mock import Mock, patch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_test_logging():
    """设置测试日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_timing_logic_separation():
    """测试定时逻辑与预约日期计算的分离"""
    print("=" * 80)
    print("测试定时逻辑修复 - 分离定时等待与预约日期计算")
    print("=" * 80)
    
    try:
        from utils.reserve import reserve
        
        # 创建 reserve 实例，设置 reserve_next_day=True
        r = reserve(reserve_next_day=True)
        
        # 模拟当前时间和目标时间
        now = datetime.datetime.now()
        current_time_str = now.strftime('%H:%M:%S')
        
        # 测试场景1：目标时间还有1分钟（类似日志中的情况）
        future_time = (now + datetime.timedelta(minutes=1)).strftime('%H:%M:%S')
        
        print(f"\n=== 测试场景1：重现日志中的问题 ===")
        print(f"当前时间: {current_time_str}")
        print(f"目标时间: {future_time} (1分钟后)")
        print(f"reserve_next_day: True")
        print(f"预期行为: 等待1分钟到今天的{future_time}，而不是明天的{future_time}")
        
        # 测试定时等待逻辑
        start_time = time.time()
        
        # 使用较短的等待时间进行测试
        test_time = (now + datetime.timedelta(seconds=3)).strftime('%H:%M:%S')
        print(f"\n实际测试: 等待到 {test_time} (3秒后)")
        
        r._wait_until_specific_time(test_time, reserve_next_day=False)  # 修复后应该传False
        
        end_time = time.time()
        actual_wait = end_time - start_time
        
        print(f"实际等待时间: {actual_wait:.2f} 秒")
        
        if 2 <= actual_wait <= 5:
            print("✅ 修复成功: 等待时间正常（约3秒）")
        elif actual_wait > 60:
            print("❌ 修复失败: 仍然等待过长时间")
        else:
            print(f"⚠️  等待时间异常: {actual_wait:.2f} 秒")
        
        # 测试预约日期计算（应该不受定时逻辑影响）
        print(f"\n=== 测试预约日期计算 ===")
        
        # 模拟日期计算逻辑
        delta_day = 1 if r.reserve_next_day else 0
        base_day = datetime.datetime.now().date()
        calculated_day = base_day + datetime.timedelta(days=0+delta_day)
        
        print(f"reserve_next_day: {r.reserve_next_day}")
        print(f"今天日期: {base_day}")
        print(f"计算的预约日期: {calculated_day}")
        
        if r.reserve_next_day and calculated_day == base_day + datetime.timedelta(days=1):
            print("✅ 预约日期计算正确: 预约明天")
        elif not r.reserve_next_day and calculated_day == base_day:
            print("✅ 预约日期计算正确: 预约今天")
        else:
            print("❌ 预约日期计算错误")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_different_scenarios():
    """测试不同的时间场景"""
    print("\n" + "=" * 80)
    print("测试不同时间场景")
    print("=" * 80)
    
    scenarios = [
        {
            "name": "reserve_next_day=True, 定时等待1分钟后",
            "reserve_next_day": True,
            "wait_minutes": 1,
            "expected_wait": "约1分钟",
            "expected_date": "明天"
        },
        {
            "name": "reserve_next_day=False, 定时等待1分钟后", 
            "reserve_next_day": False,
            "wait_minutes": 1,
            "expected_wait": "约1分钟",
            "expected_date": "今天"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        
        try:
            from utils.reserve import reserve
            
            r = reserve(reserve_next_day=scenario['reserve_next_day'])
            
            # 计算预约日期
            delta_day = 1 if r.reserve_next_day else 0
            base_day = datetime.datetime.now().date()
            calculated_day = base_day + datetime.timedelta(days=0+delta_day)
            
            print(f"reserve_next_day: {r.reserve_next_day}")
            print(f"预约日期: {calculated_day} ({scenario['expected_date']})")
            print(f"定时等待: {scenario['expected_wait']}")
            
            # 验证预约日期计算
            if scenario['reserve_next_day']:
                expected_day = base_day + datetime.timedelta(days=1)
                if calculated_day == expected_day:
                    print("✅ 预约日期正确")
                else:
                    print("❌ 预约日期错误")
            else:
                if calculated_day == base_day:
                    print("✅ 预约日期正确")
                else:
                    print("❌ 预约日期错误")
            
        except Exception as e:
            print(f"❌ 场景测试失败: {e}")

def analyze_fix():
    """分析修复内容"""
    print("\n" + "=" * 80)
    print("修复分析")
    print("=" * 80)
    
    print("🔍 问题根源:")
    print("  定时等待逻辑错误地使用了 reserve_next_day 参数")
    print("  导致预约明天座位时，也要等到明天的指定时间")
    
    print("\n🎯 修复内容:")
    print("  修复前: self._wait_until_specific_time(submit_time, self.reserve_next_day)")
    print("  修复后: self._wait_until_specific_time(submit_time, reserve_next_day=False)")
    
    print("\n✅ 修复效果:")
    print("  1. 定时等待总是等待今天的指定时间")
    print("  2. reserve_next_day 只影响预约日期计算")
    print("  3. 两个逻辑完全分离，互不干扰")
    
    print("\n📝 逻辑说明:")
    print("  - 定时等待: 等待到今天的指定时间（如18:00:00）")
    print("  - 预约日期: 根据 reserve_next_day 决定预约今天还是明天")
    print("  - 结果: 今天18:00:00提交预约明天的座位")

def main():
    """主测试函数"""
    print("定时逻辑修复测试")
    print(f"测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置日志
    setup_test_logging()
    
    # 运行测试
    success = test_timing_logic_separation()
    test_different_scenarios()
    analyze_fix()
    
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    if success:
        print("✅ 修复成功!")
        print("✅ 定时等待与预约日期计算已正确分离")
        print("✅ 不再会出现等待24小时的问题")
    else:
        print("❌ 修复可能存在问题，请检查代码")
    
    print("\n🎯 关键修复点:")
    print("1. 定时等待总是使用 reserve_next_day=False")
    print("2. 预约日期计算独立使用 self.reserve_next_day")
    print("3. 两个逻辑完全分离，避免混淆")
    
    print("\n💡 实际效果:")
    print("- 当前时间 17:59，目标时间 18:00")
    print("- 修复前: 等待1440分钟到明天18:00")
    print("- 修复后: 等待1分钟到今天18:00")
    print("- 预约日期: 仍然是明天（由 reserve_next_day 控制）")

if __name__ == "__main__":
    main()
