#!/usr/bin/env python3
"""
检查数据库中的预约数据
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import DatabaseManager, ReservationRepository
from config import config

def check_database_data():
    """检查数据库中的数据"""
    print("=== 检查数据库中的预约记录 ===")
    
    try:
        db = DatabaseManager(**config.get_database_config())
        if not db.connect():
            print("❌ 数据库连接失败")
            return
        
        repo = ReservationRepository(db)
        
        # 检查所有预约记录
        print("\n1. 所有预约记录:")
        all_reservations = repo.get_reservations_batch('worker-8083', None)
        print(f"   找到 {len(all_reservations)} 条记录")
        
        for i, r in enumerate(all_reservations):
            comment = r.get('_comment', '无备注')
            worker_id = r.get('workerid', 'N/A')
            print(f"   {i+1}. {comment} - Worker: {worker_id}")
        
        # 检查学校数据
        print("\n2. 学校数据:")
        try:
            schools = db.execute_query("SELECT * FROM schools LIMIT 5")
            print(f"   找到 {len(schools)} 条学校记录")
            for school in schools:
                print(f"   - {school}")
        except Exception as e:
            print(f"   查询学校数据失败: {e}")
        
        # 检查预约表结构
        print("\n3. 预约表结构:")
        try:
            columns = db.execute_query("DESCRIBE reservations")
            print("   字段列表:")
            for col in columns:
                print(f"   - {col['Field']}: {col['Type']}")
        except Exception as e:
            print(f"   查询表结构失败: {e}")

        # 检查用户表
        print("\n4. 用户表数据:")
        try:
            users = db.execute_query("SELECT id, username, school_id FROM users LIMIT 3")
            print(f"   找到 {len(users)} 条用户记录")
            for user in users:
                print(f"   - ID: {user.get('id')}, 用户名: {user.get('username', 'N/A')}, 学校ID: {user.get('school_id', 'N/A')}")
        except Exception as e:
            print(f"   查询用户数据失败: {e}")

        db.disconnect()
        
    except Exception as e:
        print(f"❌ 检查数据库数据时出错: {e}")

if __name__ == "__main__":
    check_database_data()
