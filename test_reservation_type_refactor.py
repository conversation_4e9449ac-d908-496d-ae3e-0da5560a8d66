#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试预约类型重构
验证 reservation_type 和 reserve_next_day 的统一处理
"""

import sys
import os
import datetime
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_test_logging():
    """设置测试日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def test_utils_reserve_refactor():
    """测试 utils/reserve.py 的重构效果"""
    print("=" * 80)
    print("测试 utils/reserve.py 的预约类型重构")
    print("=" * 80)
    
    try:
        from utils.reserve import reserve
        
        # 测试用例：不同的预约类型组合
        test_cases = [
            {
                "name": "使用 reservation_type='ADVANCE_ONE_DAY'",
                "params": {"reservation_type": "ADVANCE_ONE_DAY"},
                "expected_type": "ADVANCE_ONE_DAY",
                "expected_next_day": True
            },
            {
                "name": "使用 reservation_type='SAME_DAY'",
                "params": {"reservation_type": "SAME_DAY"},
                "expected_type": "SAME_DAY",
                "expected_next_day": False
            },
            {
                "name": "使用 reservation_type='tomorrow'（数据库格式）",
                "params": {"reservation_type": "tomorrow"},
                "expected_type": "ADVANCE_ONE_DAY",
                "expected_next_day": True
            },
            {
                "name": "使用 reservation_type='today'（数据库格式）",
                "params": {"reservation_type": "today"},
                "expected_type": "SAME_DAY",
                "expected_next_day": False
            },
            {
                "name": "向后兼容：reserve_next_day=True",
                "params": {"reserve_next_day": True},
                "expected_type": "ADVANCE_ONE_DAY",
                "expected_next_day": True
            },
            {
                "name": "向后兼容：reserve_next_day=False",
                "params": {"reserve_next_day": False},
                "expected_type": "SAME_DAY",
                "expected_next_day": False
            },
            {
                "name": "优先级测试：reservation_type 优先于 reserve_next_day",
                "params": {"reservation_type": "SAME_DAY", "reserve_next_day": True},
                "expected_type": "SAME_DAY",
                "expected_next_day": False
            }
        ]
        
        all_passed = True
        
        for case in test_cases:
            print(f"\n--- {case['name']} ---")
            
            try:
                # 创建 reserve 实例
                r = reserve(**case['params'])
                
                # 验证内部状态
                actual_type = r.reservation_type
                actual_next_day = r.reserve_next_day
                
                print(f"  参数: {case['params']}")
                print(f"  预期 reservation_type: {case['expected_type']}")
                print(f"  实际 reservation_type: {actual_type}")
                print(f"  预期 reserve_next_day: {case['expected_next_day']}")
                print(f"  实际 reserve_next_day: {actual_next_day}")
                
                # 验证结果
                type_correct = actual_type == case['expected_type']
                next_day_correct = actual_next_day == case['expected_next_day']
                
                if type_correct and next_day_correct:
                    print(f"  ✅ 测试通过")
                else:
                    print(f"  ❌ 测试失败")
                    all_passed = False
                    
            except Exception as e:
                print(f"  ❌ 测试异常: {e}")
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_xuexitong_pro_reserve_refactor():
    """测试 xuexitong_pro/utils/reserve.py 的重构效果"""
    print("\n" + "=" * 80)
    print("测试 xuexitong_pro/utils/reserve.py 的预约类型重构")
    print("=" * 80)
    
    try:
        from xuexitong_pro.utils.reserve import reserve
        
        # 测试基本功能
        test_cases = [
            {
                "name": "数据库格式：tomorrow",
                "params": {"reservation_type": "tomorrow"},
                "expected_type": "ADVANCE_ONE_DAY"
            },
            {
                "name": "业务层格式：SAME_DAY",
                "params": {"reservation_type": "SAME_DAY"},
                "expected_type": "SAME_DAY"
            },
            {
                "name": "向后兼容：reserve_next_day=True",
                "params": {"reserve_next_day": True},
                "expected_type": "ADVANCE_ONE_DAY"
            }
        ]
        
        all_passed = True
        
        for case in test_cases:
            print(f"\n--- {case['name']} ---")
            
            try:
                r = reserve(**case['params'])
                actual_type = r.reservation_type
                
                print(f"  参数: {case['params']}")
                print(f"  预期: {case['expected_type']}")
                print(f"  实际: {actual_type}")
                
                if actual_type == case['expected_type']:
                    print(f"  ✅ 测试通过")
                else:
                    print(f"  ❌ 测试失败")
                    all_passed = False
                    
            except Exception as e:
                print(f"  ❌ 测试异常: {e}")
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_date_calculation():
    """测试日期计算逻辑"""
    print("\n" + "=" * 80)
    print("测试日期计算逻辑")
    print("=" * 80)
    
    try:
        from utils.reserve import reserve
        
        base_date = datetime.datetime.now().date()
        tomorrow = base_date + datetime.timedelta(days=1)
        
        test_cases = [
            {
                "name": "SAME_DAY 应该预约今天",
                "reservation_type": "SAME_DAY",
                "expected_date": base_date
            },
            {
                "name": "ADVANCE_ONE_DAY 应该预约明天",
                "reservation_type": "ADVANCE_ONE_DAY", 
                "expected_date": tomorrow
            }
        ]
        
        print(f"今天日期: {base_date}")
        print(f"明天日期: {tomorrow}")
        
        all_passed = True
        
        for case in test_cases:
            print(f"\n--- {case['name']} ---")
            
            try:
                r = reserve(reservation_type=case['reservation_type'])
                
                # 模拟日期计算逻辑
                if r.reservation_type == 'ADVANCE_ONE_DAY':
                    calculated_date = base_date + datetime.timedelta(days=1)
                else:
                    calculated_date = base_date
                
                print(f"  预约类型: {r.reservation_type}")
                print(f"  预期日期: {case['expected_date']}")
                print(f"  计算日期: {calculated_date}")
                
                if calculated_date == case['expected_date']:
                    print(f"  ✅ 日期计算正确")
                else:
                    print(f"  ❌ 日期计算错误")
                    all_passed = False
                    
            except Exception as e:
                print(f"  ❌ 测试异常: {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n" + "=" * 80)
    print("测试向后兼容性")
    print("=" * 80)
    
    print("验证现有代码是否仍然正常工作...")
    
    try:
        from utils.reserve import reserve
        
        # 模拟现有代码的使用方式
        old_style_cases = [
            {"reserve_next_day": True, "description": "预约明天"},
            {"reserve_next_day": False, "description": "预约今天"}
        ]
        
        for case in old_style_cases:
            print(f"\n--- 向后兼容测试: {case['description']} ---")
            
            try:
                # 使用旧的参数方式
                r = reserve(reserve_next_day=case['reserve_next_day'])
                
                print(f"  旧参数: reserve_next_day={case['reserve_next_day']}")
                print(f"  内部类型: {r.reservation_type}")
                print(f"  兼容属性: {r.reserve_next_day}")
                print(f"  ✅ 向后兼容正常")
                
            except Exception as e:
                print(f"  ❌ 向后兼容失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("预约类型重构测试")
    print(f"测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置日志
    setup_test_logging()
    
    # 运行测试
    results = []
    results.append(("utils/reserve.py 重构", test_utils_reserve_refactor()))
    results.append(("xuexitong_pro/utils/reserve.py 重构", test_xuexitong_pro_reserve_refactor()))
    results.append(("日期计算逻辑", test_date_calculation()))
    results.append(("向后兼容性", test_backward_compatibility()))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if all_passed:
        print("\n🎉 重构成功!")
        print("✅ 功能重复问题已解决")
        print("✅ 向后兼容性保持良好")
        print("✅ 代码结构更加清晰")
    else:
        print("\n⚠️ 重构需要进一步调整")
    
    print("\n重构效果:")
    print("1. 统一使用 reservation_type 作为主要接口")
    print("2. 保持 reserve_next_day 的向后兼容性")
    print("3. 消除了数据库驱动代码中的不必要转换")
    print("4. 为未来扩展提供了更好的基础")

if __name__ == "__main__":
    main()
