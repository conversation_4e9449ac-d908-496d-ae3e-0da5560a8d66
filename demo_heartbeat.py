#!/usr/bin/env python3
"""
心跳功能演示脚本
展示心跳脚本的各种功能
"""

import sys
import os
import json
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def demo_basic_heartbeat():
    """演示基本心跳功能"""
    print("=== 基本心跳功能演示 ===")
    
    try:
        from heartbeat import HeartbeatClient
        
        print("1. 创建心跳客户端...")
        client = HeartbeatClient()
        
        print(f"   Worker ID: {client.worker_id}")
        print(f"   主服务器: {client.main_server_url}")
        print(f"   服务器地址: {client.worker_host}:{client.worker_port}")
        
        print("\n2. 构建心跳数据...")
        data = client._build_heartbeat_data()
        print("   心跳数据:")
        print(json.dumps(data, indent=4, ensure_ascii=False))
        
        print("\n3. 发送心跳...")
        success = client.send_heartbeat()
        
        if success:
            print("   ✓ 心跳发送成功")
        else:
            print("   ✗ 心跳发送失败")
        
        print("\n4. 查看统计信息...")
        stats = client.get_stats()
        print(f"   总尝试次数: {stats['total_attempts']}")
        print(f"   成功次数: {stats['success_count']}")
        print(f"   失败次数: {stats['failure_count']}")
        
        client.close()
        return success
        
    except Exception as e:
        print(f"   ✗ 演示失败: {e}")
        return False

def demo_custom_parameters():
    """演示自定义参数功能"""
    print("\n=== 自定义参数功能演示 ===")
    
    try:
        from heartbeat import HeartbeatClient
        
        print("1. 使用自定义参数创建客户端...")
        client = HeartbeatClient(
            worker_id="demo-worker-001",
            worker_host="*************",
            worker_port=9999
        )
        
        print(f"   自定义 Worker ID: {client.worker_id}")
        print(f"   自定义服务器地址: {client.worker_host}:{client.worker_port}")
        
        print("\n2. 发送带自定义状态的心跳...")
        success = client.send_heartbeat(status="BUSY", current_load=5)
        
        if success:
            print("   ✓ 自定义心跳发送成功")
        else:
            print("   ✗ 自定义心跳发送失败")
        
        client.close()
        return success
        
    except Exception as e:
        print(f"   ✗ 自定义参数演示失败: {e}")
        return False

def demo_error_handling():
    """演示错误处理功能"""
    print("\n=== 错误处理功能演示 ===")
    
    try:
        from heartbeat import HeartbeatClient
        
        print("1. 创建指向无效服务器的客户端...")
        # 临时修改配置指向无效服务器
        from config.settings import config
        original_url = config._config['heartbeat']['main_server_url']
        config._config['heartbeat']['main_server_url'] = 'http://invalid-server:9999'
        
        client = HeartbeatClient()
        
        print("2. 尝试发送心跳到无效服务器...")
        success = client.send_heartbeat()
        
        if not success:
            print("   ✓ 正确处理了连接错误")
        else:
            print("   ✗ 意外成功连接到无效服务器")
        
        # 恢复原始配置
        config._config['heartbeat']['main_server_url'] = original_url
        
        client.close()
        return not success  # 期望失败
        
    except Exception as e:
        print(f"   ✓ 正确捕获了异常: {e}")
        return True

def demo_multiple_heartbeats():
    """演示多次心跳发送"""
    print("\n=== 多次心跳发送演示 ===")
    
    try:
        from heartbeat import HeartbeatClient
        
        client = HeartbeatClient()
        
        print("发送5次心跳，观察统计信息变化...")
        
        for i in range(5):
            print(f"\n第 {i+1} 次心跳:")
            
            # 模拟不同的负载状态
            load = i * 2
            status = "ONLINE" if i < 3 else "BUSY"
            
            success = client.send_heartbeat(status=status, current_load=load)
            
            if success:
                print(f"   ✓ 心跳发送成功 (状态: {status}, 负载: {load})")
            else:
                print(f"   ✗ 心跳发送失败")
            
            # 短暂延迟
            time.sleep(1)
        
        print("\n最终统计信息:")
        stats = client.get_stats()
        print(f"   总尝试次数: {stats['total_attempts']}")
        print(f"   成功次数: {stats['success_count']}")
        print(f"   失败次数: {stats['failure_count']}")
        if stats['last_success_time']:
            print(f"   最后成功时间: {stats['last_success_time']}")
        
        client.close()
        return stats['success_count'] > 0
        
    except Exception as e:
        print(f"   ✗ 多次心跳演示失败: {e}")
        return False

def main():
    """主演示函数"""
    print("SeatMaster 心跳功能完整演示")
    print("=" * 60)
    
    demos = [
        ("基本心跳功能", demo_basic_heartbeat),
        ("自定义参数功能", demo_custom_parameters),
        ("错误处理功能", demo_error_handling),
        ("多次心跳发送", demo_multiple_heartbeats)
    ]
    
    passed = 0
    total = len(demos)
    
    for name, demo_func in demos:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            if demo_func():
                passed += 1
                print(f"✓ {name} 演示成功")
            else:
                print(f"✗ {name} 演示失败")
        except Exception as e:
            print(f"✗ {name} 演示异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"演示结果: {passed}/{total} 成功")
    
    if passed == total:
        print("🎉 所有功能演示成功！心跳脚本工作正常。")
        print("\n📋 使用建议:")
        print("1. 配置定时任务每30秒执行一次: python heartbeat.py")
        print("2. 监控日志文件: logs/heartbeat.log")
        print("3. 根据需要调整配置参数")
        return True
    else:
        print("⚠️  部分功能演示失败，请检查配置和网络连接。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
